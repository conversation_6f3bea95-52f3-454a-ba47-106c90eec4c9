{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { RegisterComponent } from './auth/components/register/register.component';\nimport { LoginComponent } from './auth/components/login/login.component';\nimport { EmailConfirmComponent } from './auth/components/email-confirm/email-confirm.component';\nimport { StudyGroupSearchBarComponent } from './study-group/study-group-search-bar/study-group-search.component';\nimport { PasswordRecoveryComponent } from './auth/components/password-recovery/password-recovery.component';\nimport { authGuard, loggedInGuard } from './core/security/guard/auth.guard';\nimport { StudyGroupDetailComponent } from './study-group/study-group-detail/study-group-detail.component';\nimport { StudyCreateGroupComponent } from './study-group/study-create-group/study-create-group.component';\nimport { MyStudyGroupComponent } from './study-group/my-study-group/my-study-group.component';\nimport { StudyUpdateGroupComponent } from './study-group/study-update-group/study-update-group.component';\nimport { StudyGroupAssociateComponent } from './study-group/study-group-associate/study-group-associate.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'password-recovery',\n  component: PasswordRecoveryComponent,\n  canActivate: [loggedInGuard]\n}, {\n  path: 'login',\n  component: LoginComponent,\n  canActivate: [loggedInGuard]\n},\n// prettier-ignore\n{\n  path: 'register',\n  component: RegisterComponent,\n  canActivate: [loggedInGuard]\n},\n// prettier-ignore\n{\n  path: 'confirm',\n  component: EmailConfirmComponent,\n  canActivate: [loggedInGuard]\n}, {\n  path: '',\n  component: LoginComponent,\n  canActivate: [loggedInGuard]\n}, {\n  path: '',\n  canActivate: [authGuard],\n  children: [{\n    path: 'search',\n    component: StudyGroupSearchBarComponent\n  }, {\n    path: 'create',\n    component: StudyCreateGroupComponent\n  }, {\n    path: 'detail/:groupId',\n    component: StudyGroupDetailComponent\n  }, {\n    path: 'my-study-group',\n    component: MyStudyGroupComponent\n  }, {\n    path: 'edit',\n    component: StudyUpdateGroupComponent\n  }, {\n    path: 'associate',\n    component: StudyGroupAssociateComponent\n  }, {\n    path: '',\n    redirectTo: 'search',\n    pathMatch: 'full'\n  }]\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}