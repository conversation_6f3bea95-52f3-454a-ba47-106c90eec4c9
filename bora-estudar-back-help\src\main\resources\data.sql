INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (1, 'DOMINGO');
INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (2, 'SEGUNDA');
INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (3, 'TERÇA');
INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (4, 'QUARTA');
INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (5, 'QUINTA');
INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (6, 'SEXTA');
INSERT INTO WEEKDAY (WEEK_SQ_WEEKDAY, WEEK_DS_NAME) VALUES (7, 'SÁBADO');

INSERT INTO APPLICATION_USER (APUS_SQ_USER, APUS_DS_NAME, APUS_DS_EMAIL, APUS_DS_PASSWORD, APUS_IS_ENABLED, APUS_ID_DISCORD) VALUES (100, 'ciro moraes', '<EMAIL>', '$2a$12$vUpKlDm7v5FpCAVGNYfFpOTsjPB/RvnaxwcEMHw5/7Cj6EwqyhN2C', true, null);
INSERT INTO APPLICATION_USER (APUS_SQ_USER, APUS_DS_NAME, APUS_DS_EMAIL, APUS_DS_PASSWORD, APUS_IS_ENABLED, APUS_ID_DISCORD) VALUES (101, 'rafael marinho', '<EMAIL>', '$2a$12$vUpKlDm7v5FpCAVGNYfFpOTsjPB/RvnaxwcEMHw5/7Cj6EwqyhN2C', true, null);
INSERT INTO APPLICATION_USER (APUS_SQ_USER, APUS_DS_NAME, APUS_DS_EMAIL, APUS_DS_PASSWORD, APUS_IS_ENABLED, APUS_ID_DISCORD) VALUES (102, 'isabela moraes', '<EMAIL>', '$2a$12$vUpKlDm7v5FpCAVGNYfFpOTsjPB/RvnaxwcEMHw5/7Cj6EwqyhN2C', true, null);
INSERT INTO APPLICATION_USER (APUS_SQ_USER, APUS_DS_NAME, APUS_DS_EMAIL, APUS_DS_PASSWORD, APUS_IS_ENABLED, APUS_ID_DISCORD) VALUES (103, 'caio macedo', '<EMAIL>', '$2a$12$vUpKlDm7v5FpCAVGNYfFpOTsjPB/RvnaxwcEMHw5/7Cj6EwqyhN2C', true, null);
INSERT INTO APPLICATION_USER (APUS_SQ_USER, APUS_DS_NAME, APUS_DS_EMAIL, APUS_DS_PASSWORD, APUS_IS_ENABLED, APUS_ID_DISCORD) VALUES (104, 'user user', '<EMAIL>', '$2a$12$vUpKlDm7v5FpCAVGNYfFpOTsjPB/RvnaxwcEMHw5/7Cj6EwqyhN2C', true, '210227859879493636');

INSERT INTO SUBJECT (SUBJ_SQ_SUBJECT, SUBJ_CD_CODE, SUBJ_DS_NAME) VALUES (200, 'TCC00284', 'ALGORITMOS EM GRAFOS');
INSERT INTO SUBJECT (SUBJ_SQ_SUBJECT, SUBJ_CD_CODE, SUBJ_DS_NAME) VALUES (201, 'TCC00285', 'ANÁLISE E PROJETO DE ALGORITMOS');
INSERT INTO SUBJECT (SUBJ_SQ_SUBJECT, SUBJ_CD_CODE, SUBJ_DS_NAME) VALUES (202, 'TCC00287', 'BANCO DE DADOS I');
INSERT INTO SUBJECT (SUBJ_SQ_SUBJECT, SUBJ_CD_CODE, SUBJ_DS_NAME) VALUES (203, 'TCC00286', 'ARQUITETURAS DE COMPUTADORES');

INSERT INTO STUDY_GROUP (STGR_SQ_STUDY_GROUP, STGR_DS_TITLE, STGR_DS_DESCRIPTION, STGR_USER_SQ_USER_OWNER, STGR_QT_MAX_STUDENTS, SUBJ_SQ_SUBJECT, STGR_DT_MEETING_TIME, STGR_IS_PRIVATE, STGR_DS_MODALITY, STGR_ID_DISCORD) VALUES (301, 'Ajuda ai!', 'Tenho 3 cadelas e 11 gatos', 103, 5, 200, '14:00', false, 'REMOTE', null);
INSERT INTO STUDY_GROUP_WEEKDAY (STGR_SQ_STUDY_GROUP, WEEK_SQ_WEEKDAY) VALUES (301, 1);
INSERT INTO STUDY_GROUP_WEEKDAY (STGR_SQ_STUDY_GROUP, WEEK_SQ_WEEKDAY) VALUES (301, 2);
INSERT INTO STUDY_GROUP_WEEKDAY (STGR_SQ_STUDY_GROUP, WEEK_SQ_WEEKDAY) VALUES (301, 4);
INSERT INTO STUDY_GROUP_USER (STGU_SQ_STUDY_GROUP_USER, APUS_SQ_USER, STGR_SQ_STUDY_GROUP) VALUES (500, 101, 301);
INSERT INTO STUDY_GROUP_USER (STGU_SQ_STUDY_GROUP_USER, APUS_SQ_USER, STGR_SQ_STUDY_GROUP) VALUES (501, 102, 301);
INSERT INTO STUDY_GROUP_USER (STGU_SQ_STUDY_GROUP_USER, APUS_SQ_USER, STGR_SQ_STUDY_GROUP) VALUES (502, 103, 301);

INSERT INTO STUDY_GROUP (STGR_SQ_STUDY_GROUP, STGR_DS_TITLE, STGR_DS_DESCRIPTION, STGR_USER_SQ_USER_OWNER, STGR_QT_MAX_STUDENTS, SUBJ_SQ_SUBJECT, STGR_DT_MEETING_TIME, STGR_IS_PRIVATE, STGR_DS_MODALITY, STGR_ID_DISCORD) VALUES (302, 'Help!', 'To passando um perrengue', 102, 3, 201, '15:00', false, 'PRESENTIAL', null);
INSERT INTO STUDY_GROUP_WEEKDAY (STGR_SQ_STUDY_GROUP, WEEK_SQ_WEEKDAY) VALUES (302, 2);
INSERT INTO STUDY_GROUP_WEEKDAY (STGR_SQ_STUDY_GROUP, WEEK_SQ_WEEKDAY) VALUES (302, 3);
INSERT INTO STUDY_GROUP_WEEKDAY (STGR_SQ_STUDY_GROUP, WEEK_SQ_WEEKDAY) VALUES (302, 5);
INSERT INTO STUDY_GROUP_USER (STGU_SQ_STUDY_GROUP_USER, APUS_SQ_USER, STGR_SQ_STUDY_GROUP) VALUES (503, 102, 302);