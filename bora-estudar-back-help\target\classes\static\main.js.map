{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA8C;AAEM;AACH;AACC;AACoB;AACZ;AACa;AACjB;AACQ;AACN;AACA;AACI;AACI;AACC;AACX;AACE;AACF;AACA;AACU;AACa;AACnB;AACE;AACG;AACT;AACE;AACF;AACM;AACN;AACE;AACO;AACP;AACU;AACN;;AAE5D,MAAMiC,eAAe,GAAG,CACtB/B,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB,CACjB;AAMK,MAAOE,qBAAqB;EAAA,QAAAC,CAAA;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA;UAArBF;EAAqB;EAAA,QAAAG,EAAA;cAHtBrC,0DAAY,EAAKiC,eAAe,EAnC1C/B,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB;EAAA;;;uHAOLE,qBAAqB;IAAAI,OAAA,GAHtBtC,0DAAY,EAnCtBE,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB;IAAAO,OAAA,GA/BhBrC,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AClEqC;AAC2B;AACT;AACuB;AACiB;AACL;AACT;AACO;AACA;AACZ;AACY;AACS;;;AAEnH,MAAMqB,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEV,qHAAyB;EACpCW,WAAW,EAAE,CAACT,0EAAa;CAC5B,EACD;EAAEO,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEb,kFAAc;EAAEc,WAAW,EAAE,CAACT,0EAAa;AAAC,CAAE;AAC1E;AACA;EAAEO,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEd,2FAAiB;EAAEe,WAAW,EAAE,CAACT,0EAAa;AAAC,CAAC;AAC/E;AACA;EAAEO,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEZ,yGAAqB;EAAEa,WAAW,EAAE,CAACT,0EAAa;AAAC,CAAE,EACnF;EACEO,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEb,kFAAc;EACzBc,WAAW,EAAE,CAACT,0EAAa;CAC5B,EACD;EACEO,IAAI,EAAE,EAAE;EACRE,WAAW,EAAE,CAACV,sEAAS,CAAC;EACxBW,QAAQ,EAAE,CACR;IAAEH,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEX,0HAA4BA;EAAA,CAAE,EAC3D;IAAEU,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEN,mHAAyBA;EAAA,CAAE,EACxD;IAAEK,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAEP,mHAAyBA;EAAA,CAAE,EACjE;IAAEM,IAAI,EAAE,gBAAgB;IAAEC,SAAS,EAAEL,uGAAqBA;EAAA,CAAE,EAC5D;IAAEI,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEJ,mHAAyBA;EAAA,CAAE,EACtD;IAAEG,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEH,6HAA4BA;EAAA,CAAE,EAC9D;IAAEE,IAAI,EAAE,EAAE;IAAEI,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE;EAAM,CAAE;CAExD,CACF;AAMK,MAAOC,gBAAgB;EAAA,QAAAzB,CAAA;qBAAhByB,gBAAgB;EAAA;EAAA,QAAAxB,EAAA;UAAhBwB;EAAgB;EAAA,QAAAvB,EAAA;cAHjBG,0DAAY,CAACqB,OAAO,CAACR,MAAM,CAAC,EAC5Bb,0DAAY;EAAA;;;uHAEXoB,gBAAgB;IAAAtB,OAAA,GAAAwB,0DAAA;IAAAvB,OAAA,GAFjBC,0DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AC/CgE;AACxB;AACG;AAEF;AAC8B;AAC9C;AACQ;AACF;AACuB;AACpB;AAE0B;;;;;;;ICN1EyC,4DAAA,iBAAqD;IAA7BA,wDAAA,mBAAAG,0EAAA;MAAAH,2DAAA,CAAAK,GAAA;MAAA,MAAAC,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAClDT,4DAAA,eAAU;IAAAA,oDAAA,iBAAU;IACtBA,0DADsB,EAAW,EACxB;;;;;IAHXA,wDAAA,IAAAa,iDAAA,oBAAyD;IAMzDb,4DAAA,cAAgC;IAAAA,oDAAA,GAAgD;IAAAA,0DAAA,EAAO;;;;IANvFA,2DAAA,IAAAM,MAAA,CAAAS,YAAA,IAAAT,MAAA,CAAAU,MAAA,CAAAC,GAAA,2BAIC;IAE+BjB,uDAAA,GAAgD;IAAhDA,gEAAA,yBAAAM,MAAA,CAAAc,IAAA,kBAAAd,MAAA,CAAAc,IAAA,CAAAC,IAAA,wBAAgD;;;;;;IAa9ErB,4DADF,wBAA8B,iBACa;IAAnBA,wDAAA,mBAAAsB,4DAAA;MAAAtB,2DAAA,CAAAuB,GAAA;MAAA,MAAAjB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IACtCxB,4DAAA,eAAU;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,WAAM;IAAAA,oDAAA,WAAI;IAEdA,0DAFc,EAAO,EACV,EACA;IAEXA,4DAAA,iBAAgD;IAAxBA,wDAAA,mBAAAyB,4DAAA;MAAAzB,2DAAA,CAAAuB,GAAA;MAAAvB,2DAAA;MAAA,MAAA0B,OAAA,GAAA1B,yDAAA;MAAA,OAAAA,yDAAA,CAAS0B,OAAA,CAAAE,MAAA,EAAa;IAAA,EAAC;IAC7C5B,4DAAA,eAAU;IAAAA,oDAAA,WAAI;IAChBA,0DADgB,EAAW,EAClB;;;;;;IASHA,4DAAA,YAAwD;IAAlBA,wDAAA,mBAAA6B,sEAAA;MAAA7B,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAyB,KAAA,EAAO;IAAA,EAAC;IAAC/B,oDAAA,WAAI;IAAAA,0DAAA,EAAI;IAChEA,4DAAA,YAAwD;IAAlBA,wDAAA,mBAAAgC,sEAAA;MAAAhC,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAyB,KAAA,EAAO;IAAA,EAAC;IAAC/B,oDAAA,mBAAY;IAAAA,0DAAA,EAAI;IACxEA,4DAAA,YAAgE;IAAlBA,wDAAA,mBAAAiC,sEAAA;MAAAjC,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAyB,KAAA,EAAO;IAAA,EAAC;IAAC/B,oDAAA,kBAAW;IAAAA,0DAAA,EAAI;;;;;;IAJnFA,4DAAA,mBAAc;IACZA,wDAAA,IAAAkC,kDAAA,OAAsC;IAKtClC,4DAAA,YAAoC;IAAnBA,wDAAA,mBAAAmC,wDAAA;MAAAnC,2DAAA,CAAAoC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IAACxB,oDAAA,WAAI;IAC1CA,0DAD0C,EAAI,EAC/B;;;;IANbA,uDAAA,EAIC;IAJDA,2DAAA,IAAAM,MAAA,CAAAU,MAAA,CAAAC,GAAA,2BAIC;;;;;IAKDjB,4DADF,mBAAc,YACyB;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAI;IAC9CA,4DAAA,YAAwC;IAAAA,oDAAA,eAAQ;IAClDA,0DADkD,EAAI,EACvC;;;ADhBnB,MAAOqC,YAAY;EAcvBC,YACUC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,OAAO,GAAW,kBAAkB;IACpC,KAAAC,KAAK,GAAG,oBAAoB;IAC5B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAtB,IAAI,GAAqCuB,SAAS;IAClD,KAAA5B,YAAY,GAAG,KAAK;IAGZ,KAAA6B,QAAQ,GAAG7D,qDAAM,CAACe,oEAAW,CAAC;IAC9B,KAAA+C,WAAW,GAAG9D,qDAAM,CAACC,yEAAW,CAAC;IAClC,KAAAgC,MAAM,GAAGjC,qDAAM,CAACE,mDAAM,CAAC;IACtB,KAAA6D,GAAG,GAAG/D,qDAAM,CAACD,4DAAiB,CAAC;IAC/B,KAAAiE,iBAAiB,GAAGhE,qDAAM,CAACgB,6FAAwB,CAAC;EAIzD;EAEHiD,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACH,UAAU,EAAE,CAACO,SAAS,CAAEP,UAAU,IAAI;MACrD,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAIA,UAAU,EAAE;QACd,IAAI,CAACQ,OAAO,EAAE;MAChB;MACA,IAAI,CAACJ,GAAG,CAACK,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACnC,MAAM,CAACoC,MAAM,CAACH,SAAS,CAAC,MAAK;MAChC,IAAI,CAAClC,YAAY,GAAG,IAAI,CAACC,MAAM,CAACC,GAAG,KAAK,SAAS;MAEjD,IAAG,IAAI,CAACD,MAAM,CAACC,GAAG,KAAK,SAAS,EAAC;QAC/B,IAAI,CAACuB,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACxB,MAAM,CAACC,GAAG,KAAK,iBAAiB,EAAC;QAC9C,IAAI,CAACuB,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACxB,MAAM,CAACC,GAAG,CAACoC,UAAU,CAAC,OAAO,CAAC,EAAC;QAC5C,IAAI,CAACb,OAAO,GAAG,QAAQ;MACzB,CAAC,MAAM,IAAG,IAAI,CAACxB,MAAM,CAACC,GAAG,CAACoC,UAAU,CAAC,SAAS,CAAC,EAAC;QAC9C,IAAI,CAACb,OAAO,GAAG,UAAU;MAC3B,CAAC,MAAM;QACL,IAAI,CAACA,OAAO,GAAG,kBAAkB;MACnC;IACF,CAAC,CAAC;EACJ;EAEOhB,MAAMA,CAAA;IACX,IAAI,CAACqB,WAAW,CAACrB,MAAM,EAAE,CAACyB,SAAS,CAAC;MAClCK,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACvC,MAAM,CAAC0C,aAAa,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC3B,KAAK,EAAE;QAEZ,IAAI,CAACa,QAAQ,CAACe,IAAI,CAChB,2BAA2B,EAC3B,GAAG,EACH;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEOX,OAAOA,CAAA;IACZ,IAAI,CAACL,WAAW,CAACK,OAAO,EAAE,CAACD,SAAS,CAAC;MACnCK,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACnC,IAAI,GAAGmC,IAAI;QAChB,IAAI,CAACT,GAAG,CAACK,aAAa,EAAE;MAC1B,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEApD,gBAAgBA,CAAA;IACd,IAAI,CAACO,MAAM,CAAC8C,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA/B,KAAKA,CAAA;IACH,IAAI,IAAI,CAACgC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAAChC,KAAK,EAAE;IACtB;EACF;EAAC,QAAA7E,CAAA;qBA9FUmF,YAAY,EAAArC,+DAAA,CAAAnB,qDAAA;EAAA;EAAA,QAAA1B,EAAA;UAAZkF,YAAY;IAAA6B,SAAA;IAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCjCrBrE,4DAFJ,WAAM,aACuB,qBAC4C;QAEnEA,wDAAA,IAAAuE,mCAAA,kBAA2B;QAU3BvE,uDAAA,cAA4B;QAE5BA,4DAAA,YAAqB;QAAAA,oDAAA,GAAW;QAAAA,0DAAA,EAAK;QAErCA,uDAAA,cAA4B;QAE5BA,wDAAA,IAAAyE,mCAAA,QAA2B;QAc7BzE,0DAAA,EAAc;QAGZA,4DADF,+BAAiD,yBACY;QAUvDA,wDATF,KAAA0E,oCAAA,uBAA2B,KAAAC,oCAAA,OASlB;QAMX3E,0DAAA,EAAc;QAGZA,4DADF,2BAAqB,qBACJ;QAAAA,uDAAA,YAAa;QAIpCA,0DAJoC,EAAgB,EACxB,EACA,EACpB,EACD;;;QAxDDA,uDAAA,GAQC;QARDA,2DAAA,IAAAsE,GAAA,CAAA5B,UAAA,mBAQC;QAIoB1C,uDAAA,GAAW;QAAXA,+DAAA,CAAAsE,GAAA,CAAA9B,OAAA,CAAW;QAIhCxC,uDAAA,GAaC;QAbDA,2DAAA,IAAAsE,GAAA,CAAA5B,UAAA,mBAaC;QAKC1C,uDAAA,GAcC;QAdDA,2DAAA,KAAAsE,GAAA,CAAA5B,UAAA,oBAcC;;;mBDhCD/C,iEAAU,EACVD,mEAAa,EACbD,2DAAO,EACPH,0EAAmB,EACnBC,iEAAU,EACVH,+DAAU,EACVC,gEAAW,EACXH,uDAAU,EACVM,wEAAiB,EACjBL,yDAAY,EACZS,4DAAO,EACPC,gEAAW;IAAAgF,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AErBb,MAAOnH,qBAAqB;EAGhC4E,YAAoBwC,KAAqB,EAAUjC,WAAwB;IAAvD,KAAAiC,KAAK,GAALA,KAAK;IAA0B,KAAAjC,WAAW,GAAXA,WAAW;IAF9D,KAAAkC,OAAO,GAAW,EAAE;EAE0D;EAE9E/B,QAAQA,CAAA;IAEN,IAAI,CAAC8B,KAAK,CAACE,WAAW,CAAC/B,SAAS,CAACgC,MAAM,IAAG;MACxC,MAAMC,KAAK,GAAGD,MAAM,CAAC,OAAO,CAAC;MAE7B,IAAI,CAACpC,WAAW,CAACsC,YAAY,CAACD,KAAK,CAAC,CAACjC,SAAS,CAAC;QAC7CK,IAAI,EAAGC,IAAI,IAAI;UACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;UACjB,IAAI,CAACwB,OAAO,GAAGxB,IAAI;QACrB,CAAC;QACDM,KAAK,EAAGuB,GAAG,IAAI;UACb5B,OAAO,CAACK,KAAK,CAACuB,GAAG,CAAC;UAClB,IAAI,CAACL,OAAO,GAAG,4DAA4D;QAC7E;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAAC,QAAA7H,CAAA;qBArBUQ,qBAAqB,EAAAsC,+DAAA,CAAAnB,2DAAA,GAAAmB,+DAAA,CAAAsF,yEAAA;EAAA;EAAA,QAAAnI,EAAA;UAArBO,qBAAqB;IAAAwG,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,+BAAAxB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVlCrE,4DAAA,QAAG;QAAAA,oDAAA,2BAAoB;QAAAA,0DAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;ACA+B;AACa;AAC+B;AAEjD;AACW;AACb;AACmB;AACS;;;AAqBzE,MAAOvC,cAAc;EAMzB;EACA6E,YAAA;IANQ,KAAAtB,MAAM,GAAGjC,qDAAM,CAACE,mDAAM,CAAC;IACvB,KAAAyH,EAAE,GAAG3H,qDAAM,CAAC+G,uDAAW,CAAC;IACxB,KAAAjD,WAAW,GAAG9D,qDAAM,CAACC,yEAAW,CAAC;IAE/B,KAAA2H,YAAY,GAAG,EAAE;EAEZ;EAEf3D,QAAQA,CAAA;IACN;IACA;IACA;IAEA;IACA;IACA;IAEA,IAAI,CAAC4D,SAAS,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACf,sDAAU,CAACgB,QAAQ,EAAEhB,sDAAU,CAACe,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjB,sDAAU,CAACgB,QAAQ,CAAC;KACrC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;IAE3B,MAAMC,IAAI,GAAe;MACvBL,KAAK,EAAE,IAAI,CAACF,SAAS,CAACQ,QAAQ,CAAC,OAAO,CAAC,CAACC,KAAK;MAC7CL,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACQ,QAAQ,CAAC,UAAU,CAAC,CAACC;KAC/C;IAED,IAAI,CAACxE,WAAW,CAACyE,KAAK,CAACH,IAAI,CAAC,CAAClE,SAAS,CAAC;MACrCK,IAAI,EAAGC,IAAS,IAAI;QAClBC,OAAO,CAACK,KAAK,CAAC,OAAO,EAAEN,IAAI,CAAC;QAC5B,MAAMgE,SAAS,GAAGhE,IAAI,CAACiE,EAAE,CAACC,QAAQ,EAAE;QACpCC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEJ,SAAS,CAAC;QAC5C,IAAIK,kBAAkB,GAAGrE,IAAI,CAACqE,kBAAkB;QAChDpE,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAE+D,kBAAkB,CAAC;QACvD;QAEA,IAAGA,kBAAkB,KAAK,IAAI,EAAC;UAC7B,IAAI,CAAC5G,MAAM,CAAC0C,aAAa,CAAC,SAAS,CAAC;QACtC,CAAC,MAAM;UACL,IAAI,CAAC1C,MAAM,CAAC0C,aAAa,CAAC,YAAY,CAAC;QACzC;MACF,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;QAClB,IAAI,CAAC8C,YAAY,GAAG9C,KAAK,CAACA,KAAK,CAACkB,OAAO;MACzC;KACD,CAAC;EACJ;EAAC,QAAA7H,CAAA;qBApDUO,cAAc;EAAA;EAAA,QAAAN,EAAA;UAAdM,cAAc;IAAAyG,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAkC,wBAAAzD,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3BvBrE,4DAFJ,aAAuB,eACX,qBACQ;QAACA,oDAAA,4BAAoB;QAAAA,0DAAA,EAAiB;QAGpDA,4DADF,uBAAkB,cACyD;QAAxBA,wDAAA,sBAAA+H,iDAAA;UAAA,OAAYzD,GAAA,CAAA2C,QAAA,EAAU;QAAA,EAAC;QAEpEjH,4DADF,wBAAqC,gBACxB;QAAAA,oDAAA,YAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,eAAgE;QAClEA,0DAAA,EAAiB;QAGfA,4DADF,yBAAqC,iBACxB;QAAAA,oDAAA,aAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,gBAAsE;QACxEA,0DAAA,EAAiB;QAEjBA,4DAAA,iBAKC;QACCA,oDAAA,eACF;QAAAA,0DAAA,EAAS;QAETA,4DAAA,cAAqB;QACnBA,uDAAA,eAA4B;QAC5BA,4DAAA,eAAiB;QAAAA,oDAAA,UAAE;QAAAA,0DAAA,EAAO;QAC1BA,uDAAA,eAA4B;QAC9BA,0DAAA,EAAM;QAENA,4DAAA,YAKG;QAAAA,oDAAA,mBAAW;QAAAA,0DAAA,EACb;QAEDA,4DAAA,aAKG;QAAAA,oDAAA,yBAAiB;QAK5BA,0DAL4B,EACnB,EACI,EACU,EACV,EACP;;;QA5CyBA,uDAAA,GAAuB;QAAvBA,wDAAA,cAAAsE,GAAA,CAAAsC,SAAA,CAAuB;;;mBDW9CL,2DAAO,EACPC,gEAAY,EACZC,kEAAc,EACdT,uDAAW,EAAAnH,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,6DAAA,EACXoH,+DAAmB,EAAApH,8DAAA,EAAAA,2DAAA,EACnBwH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACTC,+DAAS,EACTjH,uDAAU;IAAA2F,MAAA;EAAA;;;;;;;;;;;;;;;;;AEjBZ,MAAOjH,yBAAyB;EAAA,QAAAV,CAAA;qBAAzBU,yBAAyB;EAAA;EAAA,QAAAT,EAAA;UAAzBS,yBAAyB;IAAAsG,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAA4C,mCAAAnE,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTtCrE,4DAAA,QAAG;QAAAA,oDAAA,+BAAwB;QAAAA,0DAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACA2B;AACa;AAC+B;AAE5C;AACL;AACW;AACb;AACmB;AACS;;;AAqBzE,MAAOxC,iBAAiB;EAQ5B8E,YAAA;IAPQ,KAAAtB,MAAM,GAAGjC,qDAAM,CAACE,mDAAM,CAAC;IACvB,KAAAyH,EAAE,GAAG3H,qDAAM,CAAC+G,uDAAW,CAAC;IACxB,KAAAjD,WAAW,GAAG9D,qDAAM,CAACC,yEAAW,CAAC;IACjC,KAAA4D,QAAQ,GAAG7D,qDAAM,CAACe,oEAAW,CAAC;IAE5B,KAAA6G,YAAY,GAAG,EAAE;IAC3B,KAAAjE,UAAU,GAAG,KAAK;EACH;EAEfM,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACH,UAAU,EAAE,CAACO,SAAS,CAAEwF,QAAQ,IAAI;MACnD,IAAI,CAAC/F,UAAU,GAAG+F,QAAQ;IAC5B,CAAC,CAAC;IAEF;IACA;IACA;IAEA,IAAI,CAACC,UAAU,GAAG,IAAI,CAAChC,EAAE,CAACG,KAAK,CAAC;MAC9BxF,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC0E,sDAAU,CAACgB,QAAQ,CAAC,CAAC;MACjCD,KAAK,EAAE,CAAC,EAAE,EAAE,CAACf,sDAAU,CAACgB,QAAQ,EAAEhB,sDAAU,CAACe,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjB,sDAAU,CAACgB,QAAQ,CAAC;KACrC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACyB,UAAU,CAACxB,KAAK,EAAE;IAE5B1D,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACiF,UAAU,CAACrB,KAAK,CAAC;IAE5C,MAAMF,IAAI,GAAe;MACvB9F,IAAI,EAAE,IAAI,CAACqH,UAAU,EAAErB,KAAK,CAAChG,IAAI;MACjCyF,KAAK,EAAE,IAAI,CAAC4B,UAAU,EAAErB,KAAK,CAACP,KAAK;MACnCE,QAAQ,EAAE,IAAI,CAAC0B,UAAU,EAAErB,KAAK,CAACL;KAClC;IAED,IAAI,CAACnE,WAAW,CAAC8F,QAAQ,CAACxB,IAAI,CAAC,CAAClE,SAAS,CAAC;MACxCK,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACsF,SAAS,EAAE;MAC5B/E,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACgF,OAAO;KAC1B,CAAC;EACJ;EAEQD,SAASA,CAAA;IACf,IAAI,CAAChG,QAAQ,CAACe,IAAI,CAChB,gFAAgF,EAChF,GAAG,EACH;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACnB;IACD,IAAI,CAAC8E,UAAU,CAACI,KAAK,EAAE;IACvB,IAAI,CAAC9H,MAAM,CAAC0C,aAAa,CAAC,QAAQ,CAAC;EACrC;EAEQmF,OAAOA,CAAA;IACb,IAAI,CAACjG,QAAQ,CAACe,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAE,CAAC;EACpE;EAEQmF,UAAUA,CAAA;IAChBC,MAAM,CAACzG,QAAQ,CAAC0G,MAAM,EAAE;EAC1B;EAAC,QAAA/L,CAAA;qBA3DUM,iBAAiB;EAAA;EAAA,QAAAL,EAAA;UAAjBK,iBAAiB;IAAA0G,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAsD,2BAAA7E,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC5B1BrE,4DAFJ,aAAuB,eACX,qBACQ;QAACA,oDAAA,4BAAoB;QAAAA,0DAAA,EAAiB;QAGpDA,4DADF,uBAAkB,cACuC;QAAxBA,wDAAA,sBAAAmJ,oDAAA;UAAA,OAAY7E,GAAA,CAAA2C,QAAA,EAAU;QAAA,EAAC;QAElDjH,4DADF,wBAAqC,gBACxB;QAAAA,oDAAA,WAAI;QAAAA,0DAAA,EAAY;QAC3BA,uDAAA,eAA8D;QAChEA,0DAAA,EAAiB;QAGfA,4DADF,yBAAqC,iBACxB;QAAAA,oDAAA,aAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,gBAAgE;QAClEA,0DAAA,EAAiB;QAGfA,4DADF,yBAAqC,iBACxB;QAAAA,oDAAA,aAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,gBAAsE;QACxEA,0DAAA,EAAiB;QAEjBA,4DAAA,iBAKC;QACCA,oDAAA,mBACF;QAAAA,0DAAA,EAAS;QAETA,4DAAA,YAKG;QAAAA,oDAAA,eACH;QAIRA,0DAJQ,EAAI,EACC,EACU,EACV,EACP;;;QAnCMA,uDAAA,GAAwB;QAAxBA,wDAAA,cAAAsE,GAAA,CAAAoE,UAAA,CAAwB;;;mBDY5BnC,2DAAO,EACPC,gEAAY,EACZC,kEAAc,EACdT,uDAAW,EAAAnH,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,6DAAA,EACXoH,+DAAmB,EAAApH,8DAAA,EAAAA,2DAAA,EACnBwH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACTC,+DAAS,EACTjH,uDAAU;IAAA2F,MAAA;EAAA;;;;;;;;;;;;;;;;;;AE1B6F;;AAIzG,MAAOwE,sBAAsB;EACjCC,SAASA,CACPC,GAAqB,EACrBjG,IAAiB;IAEjBiG,GAAG,GAAGA,GAAG,CAACC,KAAK,CAAC;MACdC,eAAe,EAAE;KAClB,CAAC;IAEF,OAAOnG,IAAI,CAACoG,MAAM,CAACH,GAAG,CAAC;EACzB;EAAC,QAAArM,CAAA;qBAVUmM,sBAAsB;EAAA;EAAA,QAAAlM,EAAA;WAAtBkM,sBAAsB;IAAAM,OAAA,EAAtBN,sBAAsB,CAAAO;EAAA;;AAa5B,MAAMC,wBAAwB,GAAG,CACtC;EAAEC,OAAO,EAAEV,mEAAiB;EAAEW,QAAQ,EAAEV,sBAAsB;EAAEW,KAAK,EAAE;AAAI,CAAE,CAC9E;;;;;;;;;;;;;;;;;;;ACpB8D;AACZ;AACA;AAIA;;AAGnD,MAAMM,QAAQ,GAAG,EAAE;AAEnB,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,IAAIN,6DAAW,CAAC;IAAE,cAAc,EAAE;EAAkB,CAAE;CAChE;AAKK,MAAOlL,WAAW;EAHxBsD,YAAA;IAImB,KAAAmI,IAAI,GAAG1L,qDAAM,CAACkL,4DAAU,CAAC;IACzB,KAAAS,cAAc,GAAG3L,qDAAM,CAACsL,4DAAc,CAAC;;EAExD/C,KAAKA,CAACH,IAAgB;IACpB,OAAO,IAAI,CAACsD,IAAI,CACbE,IAAI,CAAuBL,QAAQ,CAACM,MAAM,CAAC,SAAS,CAAC,EAAEzD,IAAI,EAAEoD,WAAW,CAAC,CACzEM,IAAI,CACHT,yCAAG,CAAEhJ,IAAI,IAAI;MACX,IAAI,CAACsJ,cAAc,CAACI,QAAQ,CAAC1J,IAAI,CAAC;IACpC,CAAC,CAAC,EACF+I,gDAAU,CAAEtG,KAAK,IAAI;MACnBL,OAAO,CAACC,GAAG,CAAC,mBAAmBI,KAAK,CAACkB,OAAO,EAAE,CAAC;MAC/C,IAAI,CAAC2F,cAAc,CAACK,KAAK,EAAE;MAC3B,MAAMlH,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA8E,QAAQA,CAACxB,IAAgB;IACvB,OAAO,IAAI,CAACsD,IAAI,CACbE,IAAI,CAAuBL,QAAQ,CAACM,MAAM,CAAC,SAAS,CAAC,EAAEzD,IAAI,EAAEoD,WAAW,CAAC,CACzEM,IAAI,CACHV,gDAAU,CAAEtG,KAAK,IAAI;MACnBL,OAAO,CAACC,GAAG,CAAC,mBAAmBI,KAAK,CAACkB,OAAO,EAAE,CAAC;MAC/C,IAAI,CAAC2F,cAAc,CAACK,KAAK,EAAE;MAC3B,MAAMlH,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAsB,YAAYA,CAACD,KAAa;IACxB,OAAO,IAAI,CAACuF,IAAI,CAACO,GAAG,CAClBV,QAAQ,CAACM,MAAM,CAAC,kBAAkB1F,KAAK,EAAE,CAAC,EAC1CqF,WAAW,CACZ;EACH;EAEA/I,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACiJ,IAAI,CAACE,IAAI,CAACL,QAAQ,GAAG,UAAU,EAAE,EAAE,EAAEC,WAAW,CAAC,CAACM,IAAI,CAChET,yCAAG,CAAC,MAAK;MACP,IAAI,CAACM,cAAc,CAACK,KAAK,EAAE;IAC7B,CAAC,CAAC,EACFZ,gDAAU,CAAEtG,KAAK,IAAI;MACnBL,OAAO,CAACC,GAAG,CAAC,mBAAmBI,KAAK,CAACkB,OAAO,EAAE,CAAC;MAC/C,IAAI,CAAC2F,cAAc,CAACK,KAAK,EAAE;MAC3B,MAAMlH,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAX,OAAOA,CAAA;IACL,OAAO,IAAI,CAACwH,cAAc,CAACxH,OAAO,EAAE;EACtC;EAEAR,UAAUA,CAAA;IACR,OAAO,IAAI,CAACgI,cAAc,CAAChI,UAAU,EAAE;EACzC;EAAC,QAAAxF,CAAA;qBAzDU8B,WAAW;EAAA;EAAA,QAAA7B,EAAA;WAAX6B,WAAW;IAAA2K,OAAA,EAAX3K,WAAW,CAAA4K,IAAA;IAAAqB,UAAA,EAFV;EAAM;;;;;;;;;;;;;;;;;;ACf+B;AAEzB;;AAE1B,MAAMG,WAAW,GAAG,aAAa;AAK3B,MAAOf,cAAc;EAMzB/H,YAAA;IALQ,KAAA+I,UAAU,GAAG,IAAIH,iDAAe,CACtCvI,SAAS,CACV;IACO,KAAA2I,aAAa,GAAG,IAAIJ,iDAAe,CAAU,KAAK,CAAC;IAGzD,IAAI,CAACK,mBAAmB,EAAE;EAC5B;EAEAR,KAAKA,CAAA;IACHrD,YAAY,CAACqD,KAAK,EAAE;IACpB,IAAI,CAACM,UAAU,CAAC/H,IAAI,CAACX,SAAS,CAAC;IAC/B,IAAI,CAAC2I,aAAa,CAAChI,IAAI,CAAC,KAAK,CAAC;EAChC;EAEOwH,QAAQA,CAAC1J,IAA0B;IACxCsG,YAAY,CAAC8D,UAAU,CAACJ,WAAW,CAAC;IACpC1D,YAAY,CAACC,OAAO,CAACyD,WAAW,EAAEK,IAAI,CAACC,SAAS,CAACtK,IAAI,CAAC,CAAC;IACvD,IAAI,CAACiK,UAAU,CAAC/H,IAAI,CAAClC,IAAI,CAAC;IAC1B,IAAI,CAACkK,aAAa,CAAChI,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEOJ,OAAOA,CAAA;IACZ,MAAMyI,gBAAgB,GAAGjE,YAAY,CAACkE,OAAO,CAACR,WAAW,CAAC;IAC1D,IAAI,CAACO,gBAAgB,EAAE;MACrB,MAAM,IAAIE,KAAK,CAAC,8CAA8C,CAAC;IACjE;IAEA,MAAMR,UAAU,GAAyBI,IAAI,CAACK,KAAK,CAACH,gBAAgB,CAAC;IACrE,OAAOR,wCAAE,CAACE,UAAU,CAAC;EACvB;EAEO3I,UAAUA,CAAA;IACf,OAAO,IAAI,CAAC4I,aAAa,CAACS,YAAY,EAAE;EAC1C;EAEQR,mBAAmBA,CAAA;IACzB,IAAI,CAAC7D,YAAY,CAACkE,OAAO,CAACR,WAAW,CAAC,EAAE;MACtC;IACF;IAEA,MAAMY,aAAa,GAAGtE,YAAY,CAACkE,OAAO,CAACR,WAAW,CAAE;IACxD,MAAMa,aAAa,GAAGR,IAAI,CAACK,KAAK,CAACE,aAAa,CAAC;IAE/C,IAAI,CAACX,UAAU,CAAC/H,IAAI,CAAC2I,aAAa,CAAC;IACnC,IAAI,CAACX,aAAa,CAAChI,IAAI,CAAC,IAAI,CAAC;EAC/B;EAAC,QAAApG,CAAA;qBA/CUmN,cAAc;EAAA;EAAA,QAAAlN,EAAA;WAAdkN,cAAc;IAAAV,OAAA,EAAdU,cAAc,CAAAT,IAAA;IAAAqB,UAAA,EAFb;EAAM;;;;;;;;;;;;;;;;;;;;ACFK;AACc;AACY;AAEnD;;;;;;;;AAQO,MAAMnN,aAAa,GAAkBA,CAC1CoO,MAA8B,EAC9BC,MAA2B,KACN;EACrB3I,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAC1D,MAAMZ,WAAW,GAAG9D,qDAAM,CAACC,2DAAW,CAAC;EACvC,MAAMgC,MAAM,GAAGjC,qDAAM,CAACE,mDAAM,CAAC;EAC7B,IAAIyD,UAAU,GAAG,KAAK;EAEtBG,WAAW,CAACH,UAAU,EAAE,CAACO,SAAS,CAAEmJ,CAAC,IAAI;IACvC1J,UAAU,GAAG0J,CAAC;EAChB,CAAC,CAAC;EAEF,IAAI1J,UAAU,EAAE;IACd1B,MAAM,CAAC0C,aAAa,CAAC,SAAS,CAAC;IAC/B,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AAED;;;;;;;;AAQO,MAAM7F,SAAS,GAAkBA,CACtCqO,MAA8B,EAC9BC,MAA2B,KACN;EACrB3I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;EACtD,MAAMZ,WAAW,GAAG9D,qDAAM,CAACC,2DAAW,CAAC;EACvC,MAAMgC,MAAM,GAAGjC,qDAAM,CAACE,mDAAM,CAAC;EAC7B,IAAIyD,UAAU,GAAG,KAAK;EAEtBG,WAAW,CAACH,UAAU,EAAE,CAACO,SAAS,CAAEmJ,CAAC,IAAI;IACvC1J,UAAU,GAAG0J,CAAC;EAChB,CAAC,CAAC;EAEF,IAAI,CAAC1J,UAAU,EAAE;IACf1B,MAAM,CAAC0C,aAAa,CAAC,QAAQ,CAAC;IAC9B,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb,CAAC;AAED;;;;;;;;AAQO,MAAM2I,qBAAqB,GAAkBA,CAClDH,MAA8B,EAC9BC,MAA2B,KACN;EACrB3I,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;EAClE,MAAMZ,WAAW,GAAG9D,qDAAM,CAACC,2DAAW,CAAC;EACvC,MAAMgC,MAAM,GAAGjC,qDAAM,CAACE,mDAAM,CAAC;EAE7B,MAAMqN,MAAM,GAAG5E,YAAY,CAACkE,OAAO,CAAC,aAAa,CAAC;EAElD,IAAIU,MAAM,EAAE;IACV,MAAMjB,UAAU,GAAGI,IAAI,CAACK,KAAK,CAACQ,MAAM,CAAC;IACrC,MAAM1E,kBAAkB,GAAGyD,UAAU,CAACzD,kBAAkB;IACxD,IAAG,CAACA,kBAAkB,EAAC;MACrB5G,MAAM,CAAC0C,aAAa,CAAC,YAAY,CAAC;IACpC;EACF,CAAC,MAAM;IACLF,OAAO,CAACK,KAAK,CAAC,wCAAwC,CAAC;EACzD;EACA;EACA,OAAO,IAAI;AACb,CAAC;;;;;;;;;;;;;;;;;;;;AC/FgE;AACW;AAC3B;AACQ;AACF;;;;ICY/C7D,4DAAA,WAAgC;IAAAA,oDAAA,GAAS;IAAAA,0DAAA,EAAI;;;;IAAbA,uDAAA,EAAS;IAATA,+DAAA,CAAAuM,MAAA,CAAS;;;ADM3C,MAAOC,eAAe;EAhB5BlK,YAAA;IAkBE,KAAAmK,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAAC1P,CAAC,EAAE2P,CAAC,KAAK,YAAYA,CAAC,GAAG,CAAC,EAAE,CAAC;;EAYtE,QAAA3P,CAAA;qBAdYsP,eAAe;EAAA;EAAA,QAAArP,EAAA;UAAfqP,eAAe;IAAAtI,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAkH,yBAAAzI,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCrBxBrE,4DAFJ,aAA8B,qBACyC,gBACnB;QAAxBA,wDAAA,mBAAA+M,iDAAA;UAAA/M,2DAAA,CAAAK,GAAA;UAAA,MAAA2M,OAAA,GAAAhN,yDAAA;UAAA,OAAAA,yDAAA,CAASgN,OAAA,CAAApL,MAAA,EAAa;QAAA,EAAC;QAC7C5B,4DAAA,eAAU;QAAAA,oDAAA,WAAI;QAChBA,0DADgB,EAAW,EAClB;QACTA,uDAAA,cAA4B;QAC5BA,4DAAA,YAAqB;QAAAA,oDAAA,uBAAgB;QACvCA,0DADuC,EAAK,EAC9B;QAQVA,4DANJ,+BAAiD,wBAK9C,oBACe;QACZA,8DAAA,KAAAkN,+BAAA,gBAAAlN,uEAAA,CAEC;QAITA,0DAHM,EAAe,EACH,EACQ,EACpB;;;QANEA,uDAAA,IAEC;QAFDA,wDAAA,CAAAsE,GAAA,CAAAmI,SAAA,CAEC;;;mBDJD9M,iEAAU,EACVD,mEAAa,EACbD,2DAAO,EACPH,0EAAmB,EACnBC,iEAAU,EACVH,8DAAU,EACVC,+DAAW;IAAAwF,MAAA;EAAA;;;;;;;;;;;;;;;;;;;AEnB4B;AACwB;AACJ;;AAM7D,MAAOwI,YAAY;EAAA,QAAAnQ,CAAA;qBAAZmQ,YAAY;EAAA;EAAA,QAAAlQ,EAAA;UAAZkQ;EAAY;EAAA,QAAAjQ,EAAA;cAHXrC,yDAAY,EAAEkC,2EAAqB,EAAEuP,gFAAe;EAAA;;;sHAGrDa,YAAY;IAAAhQ,OAAA,GAHXtC,yDAAY,EAAEkC,2EAAqB,EAAEuP,gFAAe;IAAAlP,OAAA,GACpDkP,gFAAe;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACJW;AAC+B;AAClB;AACuB;AACN;AACrB;AACE;AACI;AACsD;;;;;;;;;;;;ICLrGxM,4DAAA,qBAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAFsCA,wDAAA,UAAAyN,SAAA,CAAAC,IAAA,WAAAD,SAAA,CAAAhL,KAAA,CAA4C;IAC7FzC,uDAAA,EACF;IADEA,gEAAA,OAAAyN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAAhL,KAAA,MACF;;;;;IAgDJzC,4DAAA,UAAoC;IAClCA,uDAAA,sCAAiF;IACnFA,0DAAA,EAAM;;;;IADyBA,uDAAA,EAAqB;IAArBA,wDAAA,eAAA4N,SAAA,CAAqB;;;ADxBlD,MAAO3P,qBAAqB;EAShCqE,YACUQ,GAAsB,EACvB+K,OAA0B,EACzB7M,MAAc;IAFd,KAAA8B,GAAG,GAAHA,GAAG;IACJ,KAAA+K,OAAO,GAAPA,OAAO;IACN,KAAA7M,MAAM,GAANA,MAAM;IAXhB,KAAA8M,OAAO,GAAU,EAAE;IAEnB,KAAAC,YAAY,GAAgB,IAAIC,GAAG,EAAE;IACrC,KAAAC,YAAY,GAAW,EAAE;EAStB;EAEHjL,QAAQA,CAAA;IACN,MAAMuE,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMpE,EAAE,GAAG0G,MAAM,CAAC3G,SAAS,CAAC;IAE5B,IAAI,CAACsG,OAAO,CAACM,kBAAkB,CAAC3G,EAAE,CAAC,CAACvE,SAAS,CAAEmL,KAAK,IAAI;MACtD5K,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2K,KAAK,CAAC;MACvC,IAAI,CAACP,OAAO,CAACQ,aAAa,GAAGD,KAAK;MAClC,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACtH,KAAK,CAACuH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAACQ,aAAa,CAACG,MAAM,CAACK,MAAM,IAC7DA,MAAM,CAACpM,KAAK,CAACmM,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CACpG;EACH;EAEAM,YAAYA,CAAA;IACV,MAAMN,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACtH,KAAK,CAACuH,WAAW,EAAE;IAEhE;IACA,MAAM,CAACI,UAAU,EAAEC,WAAW,CAAC,GAAGR,WAAW,CAACS,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAAC;IAEnF,MAAMb,MAAM,GAAG,IAAI,CAACX,OAAO,CAACQ,aAAa,EAAEG,MAAM,CAACK,MAAM,IACtD,IAAI,CAACS,iBAAiB,CAACT,MAAM,CAAC,IAC9B,IAAI,CAACU,YAAY,CAACV,MAAM,CAAC,KACxBA,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACE,UAAU,CAAC,IAC9CH,MAAM,CAACpM,KAAK,CAACmM,WAAW,EAAE,CAACE,QAAQ,CAACG,WAAW,CAAC,CAAC,CACnD,IAAI,EAAE;IAEP,IAAI,CAACnB,OAAO,GAAG,CAAC,GAAGU,MAAM,CAAC;IAC1B,IAAI,CAAC1L,GAAG,CAACK,aAAa,EAAE;EAC1B;EAEAqM,YAAYA,CAAA;IACV,IAAI,CAACd,KAAK,CAACC,aAAa,CAACtH,KAAK,GAAG,EAAE;IACnC,IAAI,CAACoI,IAAI,CAACd,aAAa,CAACtH,KAAK,GAAG,EAAE;IAClC,IAAI,CAACqI,UAAU,CAACC,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAG,KAAK,CAAC;IAC7D,IAAI,CAAC/M,GAAG,CAACK,aAAa,EAAE;EAC1B;EAEAmM,iBAAiBA,CAACT,MAAW;IAC3B,IAAI,CAACA,MAAM,CAACiB,UAAU,IAAI,IAAI,CAAC/B,YAAY,CAACgC,IAAI,KAAK,CAAC,EAAE;MACtD,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOlB,MAAM,CAACiB,UAAU,CAACE,IAAI,CAAEC,GAAW,IAAK,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAACrB,WAAW,EAAE,CAAC,CAAC;EAC1F;EAEAW,YAAYA,CAACV,MAAW;IACtB,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE;MACtB,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOY,MAAM,CAACsB,IAAI,IAAI,IAAI,CAAClC,YAAY;EACzC;EAEAmC,IAAIA,CAACH,GAAW;IACd,IAAI,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAClC,YAAY,CAACsC,MAAM,CAACJ,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAAClC,YAAY,CAACuC,GAAG,CAACL,GAAG,CAAC;IAC5B;EACF;EAEAM,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACvC,YAAY,GAAGuC,KAAK,CAACC,MAAM,CAACpJ,KAAK;EACxC;EAAC,QAAAnK,CAAA;qBAlFUe,qBAAqB,EAAA+B,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAnB,mEAAA,GAAAmB,+DAAA,CAAAsF,mDAAA;EAAA;EAAA,QAAAnI,EAAA;UAArBc,qBAAqB;IAAAiG,SAAA;IAAAC,SAAA,WAAAwM,4BAAAtM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;kEAOlBkJ,mEAAW;;;;;;;;;;;;;;;;;QCrCrBvN,4DAHN,aAA8B,aACJ,wBAC6C,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAsH;QAArBA,wDAAnB,mBAAA4Q,sDAAA;UAAA5Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC,mBAAAqC,sDAAA;UAAA7Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC;QAAnHxO,0DAAA,EAAsH;QACtHA,4DAAA,6BAA2D;QACzDA,wDAAA,IAAA8Q,2CAAA,yBAAgG;QAIpG9Q,0DADE,EAAmB,EACJ;QAEjBA,4DAAA,kBAI2B;QAAzBA,wDAAA,mBAAA+Q,wDAAA;UAAA/Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAyK,YAAA,EAAc;QAAA,EAAC;QACxB/O,4DAAA,oBAAkC;QAAAA,oDAAA,cAAM;QAC1CA,0DAD0C,EAAW,EAC5C;QAETA,4DAAA,kBAIyB;QAAzBA,wDAAA,mBAAAgR,wDAAA;UAAAhR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAkL,YAAA,EAAc;QAAA,EAAC;QAACxP,oDAAA,sBACzB;QACFA,0DADE,EAAS,EACL;QAGJA,4DADF,eAA0B,kBAC8C;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAS;QAIvFA,4DAHN,uBAA8D,eACtC,mBACY,wBACgD;QAAhDA,wDAAA,mBAAAiR,8DAAAC,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAoR,8DAAAF,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAqR,8DAAAH,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAsR,8DAAAJ,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAuR,8DAAAL,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAwR,8DAAAN,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAyR,8DAAAP,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAGvFA,0DAHuF,EAAe,EACxF,EACN,EACG;QAEXA,4DAAA,kBAA0E;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAS;QAG7FA,4DAFJ,uBAA+C,eACvB,0BACmE;QAAnCA,wDAAA,mBAAA0R,gEAAAR,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASkR,MAAA,CAAAC,eAAA,EAAwB;QAAA,EAAC;QACpFnR,4DAAA,iBAAW;QAAAA,oDAAA,oBAAY;QAAAA,0DAAA,EAAY;QACnCA,4DAAA,oBAAmE;QAAjCA,wDAAA,oBAAA2R,wDAAAT,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUsE,GAAA,CAAAiM,YAAA,CAAAW,MAAA,CAAoB;QAAA,EAAC;QAIzElR,0DAJQ,EAAmE,EACpD,EACb,EACG,EACP;QAENA,4DAAA,eAAuB;QACrBA,wDAAA,KAAA4R,qCAAA,kBAAoC;QAIxC5R,0DADE,EAAM,EACF;;;;;;QAxDqDA,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA6R,OAAA,CAAwB;QAE5C7R,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAsE,GAAA,CAAAgK,eAAA,CAAkB;QAuBlCtO,uDAAA,GAA0B;QAA1BA,wDAAA,sBAAA8R,OAAA,CAA0B;QAe1B9R,uDAAA,IAA8B;QAA9BA,wDAAA,sBAAA+R,WAAA,CAA8B;QAYzB/R,uDAAA,IAAU;QAAVA,wDAAA,YAAAsE,GAAA,CAAAwJ,OAAA,CAAU;;;mBDtClCzH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACTzG,2DAAO,EACPzD,iEAAa,EAAAgW,2DAAA,EAAAA,kEAAA,EACbzW,yEAAiB,EAAA2W,mEAAA,EAEjB5E,mDAAK,EACLE,qHAA6B,EAC7BrS,kFAAqB,EAAAgX,4EAAA,EAAAE,8DAAA,EAAAF,mFAAA;IAAAtN,MAAA;EAAA;;;;;;;;;;;;;;;;;;AE3BiC;AAC5B;;;AAKxB,MAAO9E,wBAAwB;EAMnCuC,YAAoBtB,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAHlB,KAAAyR,YAAY,GAAa,EAAE;IAC3B,KAAAC,UAAU,GAAkB,IAAI;IAGtC,IAAI,CAAC1R,MAAM,CAACoC,MAAM,CACfyH,IAAI,CACH2D,4CAAM,CAACgC,KAAK,IAAIA,KAAK,YAAYgC,4DAAe,CAAC,CAClD,CACAvP,SAAS,CAAEuN,KAAK,IAAI;MACnB,MAAMmC,oBAAoB,GAAGnC,KAAwB;MACrD,IAAI,IAAI,CAACkC,UAAU,EAAE;QACnB,IAAI,CAACD,YAAY,CAACG,IAAI,CAAC,IAAI,CAACF,UAAU,CAAC;MACzC;MACA,IAAI,CAACA,UAAU,GAAGC,oBAAoB,CAAC1R,GAAG;MAC1CuC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACiP,UAAU,CAAC,CAAC,CAAC;MAC9ClP,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACgP,YAAY,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;EACN;EAEOI,cAAcA,CAAA;IACnB,OAAO,IAAI,CAACJ,YAAY,CAAC7F,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6F,YAAY,CAAC,IAAI,CAACA,YAAY,CAAC7F,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9F;EAEOkG,kBAAkBA,CAAA;IACvB,MAAMC,WAAW,GAAG,IAAI,CAACF,cAAc,EAAE;IACzC,IAAIE,WAAW,EAAE;MACf,IAAI,CAAC/R,MAAM,CAAC0C,aAAa,CAACqP,WAAW,CAAC,CAACC,KAAK,CAACnP,KAAK,IAAIL,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC,CAAC;MAChG;MACA,IAAI,CAAC4O,YAAY,CAACQ,GAAG,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAACjS,MAAM,CAAC8C,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAACkP,KAAK,CAACnP,KAAK,IAAIL,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC,CAAC;IAC7F;EACF;EAAC,QAAA3G,CAAA;qBAnCU6C,wBAAwB,EAAAC,sDAAA,CAAAnB,mDAAA;EAAA;EAAA,QAAA1B,EAAA;WAAxB4C,wBAAwB;IAAA4J,OAAA,EAAxB5J,wBAAwB,CAAA6J,IAAA;IAAAqB,UAAA,EAFvB;EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLkC;AAEkD;AACjC;AACH;AACI;AACrB;AAEmB;AAEnB;AACE;AAGT;AACC;AAEmB;;;;;;;;;;;;;ICVtDjL,4DAAA,qBAAoE;IAElEA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAHsCA,wDAAA,UAAAyN,SAAA,CAAgB;IAEjEzN,uDAAA,EACF;IADEA,gEAAA,OAAAyN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAApM,IAAA,MACF;;;;;IA8BJrB,4DAAA,0BAA8D;IAC5DA,oDAAA,GACF;IAAAA,0DAAA,EAAkB;;;;IAF8BA,wDAAA,UAAAyT,MAAA,CAAa;IAC3DzT,uDAAA,EACF;IADEA,gEAAA,MAAAyT,MAAA,CAAApS,IAAA,MACF;;;ADQA,MAAOrD,yBAAyB;EAwBpCsE,YACUM,QAAqB,EACrB8Q,cAA8B,EAC9B1S,MAAc,EACd8B,GAAsB,EACvB+K,OAA0B,EACzB8F,OAA2B;IAL3B,KAAA/Q,QAAQ,GAARA,QAAQ;IACR,KAAA8Q,cAAc,GAAdA,cAAc;IACd,KAAA1S,MAAM,GAANA,MAAM;IACN,KAAA8B,GAAG,GAAHA,GAAG;IACJ,KAAA+K,OAAO,GAAPA,OAAO;IACN,KAAA8F,OAAO,GAAPA,OAAO;IA5BjB,KAAA7F,OAAO,GAAU,EAAE;IAIT,KAAAgC,UAAU,GAAmC,CACrD;MAAEtI,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,CACvB;IAES,KAAAuS,UAAU,GAA2C,CAC7D;MAAEvM,KAAK,EAAE,YAAY;MAAEwM,SAAS,EAAE;IAAY,CAAE,EAChD;MAAExM,KAAK,EAAE,QAAQ;MAAEwM,SAAS,EAAE;IAAQ,CAAE,EACxC;MAAExM,KAAK,EAAE,SAAS;MAAEwM,SAAS,EAAE;IAAS,CAAE,CAC3C;IAES,KAAAC,gBAAgB,GAAkB,IAAI;IAgDxC,KAAAC,YAAY,GAAwB,IAAI;IAtC9C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC9M,KAAK,CAAC;MACnCpE,KAAK,EAAE,CAAC,EAAE,CAAC;MACXwR,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,QAAQ,CAAC;MACpBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEAvR,QAAQA,CAAA;IACN,IAAI,CAAC6K,OAAO,CAAC2G,WAAW,EAAE,CAACvR,SAAS,CAAEmL,KAAK,IAAI;MAC7C5K,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEuK,KAAK,CAAC;MACzC,IAAI,CAACP,OAAO,CAAC4G,QAAQ,GAAGrG,KAAK;MAC7B,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACtH,KAAK,CAACuH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAAC4G,QAAQ,CAACjG,MAAM,CAChDK,MAAM,IACLA,MAAM,CAACrH,EAAE,CAACC,QAAQ,EAAE,CAACqH,QAAQ,CAACL,WAAW,CAAC,IAC1CI,MAAM,CAACxN,IAAI,CAACuN,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAC/CI,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CAClD;EACH;EAEAiG,gBAAgBA,CAAC7F,MAAW;IAC1B,IAAI,CAACmF,UAAU,CAAChJ,GAAG,CAAC,SAAS,CAAC,EAAE2J,UAAU,CAAC9F,MAAM,CAAC;IAClD,IAAI,CAACmF,UAAU,CACZhJ,GAAG,CAAC,aAAa,CAAC,EACjB2J,UAAU,CAAC9F,MAAM,CAACnB,IAAI,GAAG,KAAK,GAAGmB,MAAM,CAACxN,IAAI,CAAC;EACnD;EAIAuT,YAAYA,CAAA;IACV,MAAMrN,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMpE,EAAE,GAAG0G,MAAM,CAAC3G,SAAS,CAAC;IAC5B,MAAM9E,KAAK,GACT,IAAI,CAACuR,UAAU,EAAE3M,KAAK,CAAC8M,OAAO,CAACzG,IAAI,GACnC,GAAG,GACH,IAAI,CAACsG,UAAU,EAAE3M,KAAK,CAAC8M,OAAO,CAAC9S,IAAI;IAErC,MAAMwT,cAAc,GAAG;MACrBpS,KAAK,EAAEA,KAAK;MACZwR,WAAW,EAAE,IAAI,CAACD,UAAU,EAAE3M,KAAK,CAAC4M,WAAW;MAC/Ca,OAAO,EAAEtN,EAAE;MACX2M,OAAO,EAAE,IAAI,CAACH,UAAU,EAAE3M,KAAK,CAAC8M,OAAO;MACvCI,QAAQ,EAAE,IAAI,CAACP,UAAU,EAAE3M,KAAK,CAACkN,QAAQ;MACzCF,WAAW,EAAE,IAAI,CAACL,UAAU,EAAE3M,KAAK,CAACgN,WAAW;MAC/CD,WAAW,EAAE,IAAI,CAACJ,UAAU,EAAE3M,KAAK,CAAC+M,WAAW;MAC/CE,QAAQ,EAAE,IAAI,CAACN,UAAU,EAAE3M,KAAK,CAACiN;KAClC;IAED,IAAI,CAACP,YAAY,GAAG,IAAI,CAAClG,OAAO,CAC7BkH,gBAAgB,CAACF,cAAc,CAAC,CAChC5R,SAAS,CAAC;MACTK,IAAI,EAAG0R,QAAQ,IAAI;QACjBxR,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAEmR,QAAQ,CAAC;QAE9D,MAAMC,gBAAgB,GAAG,IAAI,CAACpH,OAAO,CAACqH,iBAAiB,CAACF,QAAQ,CAAC;QACjE,IAAI,CAACnH,OAAO,CAACsH,aAAa,CAACF,gBAAgB,CAAC;QAE5C,IAAI,CAACrS,QAAQ,CAACe,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;UAC7DC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAAC5C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,WAAWkR,QAAQ,CAACxN,EAAE,EAAE,CAAC,CAAC;MAClD,CAAC;MACD3D,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACN;EAEAuR,WAAWA,CAAA;IACT,IAAI,CAACrB,YAAY,EAAEsB,WAAW,EAAE,CAAC,CAAC;EACpC;EAAC,QAAAnY,CAAA;qBAjHUc,yBAAyB,EAAAgC,+DAAA,CAAAnB,oEAAA,GAAAmB,+DAAA,CAAAsF,uDAAA,GAAAtF,+DAAA,CAAAgS,mDAAA,GAAAhS,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAkS,mEAAA,GAAAlS,+DAAA,CAAAmS,8DAAA;EAAA;EAAA,QAAAhV,EAAA;UAAzBa,yBAAyB;IAAAkG,SAAA;IAAAC,SAAA,WAAAoR,gCAAAlR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;gFAHzB,CAACkP,uDAAc,CAAC,GAAAvT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAA4P,mCAAAnR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC3CrBrE,4DAJR,aAA8B,cACI,aACb,wBACoD,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAoJ;QAArBA,wDAAnB,mBAAAyV,0DAAA;UAAAzV,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC,mBAAAkH,0DAAA;UAAA1V,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC;QAAjJxO,0DAAA,EAAoJ;QACpJA,4DAAA,6BAAoH;QAAzDA,wDAAA,4BAAA2V,8EAAAzE,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAkBsE,GAAA,CAAAoQ,gBAAA,CAAAxD,MAAA,CAAArC,MAAA,CAAAxH,KAAA,CAAqC;QAAA,EAAC;QACjHrH,wDAAA,KAAA4V,gDAAA,wBAAoE;QAM1E5V,0DAFI,EAAmB,EACJ,EACb;QAGJA,4DADF,yBAAiC,iBACpB;QAAAA,oDAAA,2BAAS;QAAAA,0DAAA,EAAY;QAChCA,uDAAA,oBAA4D;QAC9DA,0DAAA,EAAiB;QAIbA,4DAFJ,cAAiB,sBACC,iBACH;QAAAA,oDAAA,6BAAgB;QAAAA,0DAAA,EAAY;QAErCA,4DADF,sBAA0C,sBAChB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAE7BA,0DAF6B,EAAa,EAC3B,EACE;QAGfA,4DADF,0BAAuC,iBAC1B;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAY;QACrCA,uDAAA,iBAA2D;QAE/DA,0DADE,EAAiB,EACb;QAENA,4DAAA,UAAI;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAK;QACvBA,4DAAA,4BAAsD;QACpDA,wDAAA,KAAA6V,qDAAA,6BAA8D;QAGhE7V,0DAAA,EAAmB;QAEnBA,4DAAA,eAAuB;QACrBA,uDAAA,eAAW;QAEXA,4DAAA,kBAAoF;QAAzBA,wDAAA,mBAAA8V,4DAAA;UAAA9V,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAsQ,YAAA,EAAc;QAAA,EAAC;QAAC5U,oDAAA,mBAAW;QAGrGA,0DAHqG,EAAS,EACpG,EACD,EACH;;;;QAlDEA,uDAAA,EAAwB;QAAxBA,wDAAA,cAAAsE,GAAA,CAAA0P,UAAA,CAAwB;QAI2DhU,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA6R,OAAA,CAAwB;QAE1E7R,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAsE,GAAA,CAAAgK,eAAA,CAAkB;QAiBrCtO,uDAAA,IAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QAYMA,uDAAA,GAAa;QAAbA,wDAAA,YAAAsE,GAAA,CAAAwL,UAAA,CAAa;;;mBDdhD/U,yDAAY,EAAAsX,oDAAA,EACZpM,+DAAmB,EAAAkM,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAInB9L,sEAAY,EACZC,kEAAQ,EACR+M,+DAAS,EACTrN,uDAAW,EAEXsM,6DAAS,EACTa,oEAAc,EACdC,mEAAa,EACbhN,8DAAQ,EACRF,gEAAS,EACT/K,kFAAqB,EAAA6a,4EAAA,EAAAA,mFAAA,EACrB1C,uDAAW,EAAA2C,iDAAA,EACXzC,2EAAqB;IAAA3O,MAAA;EAAA;;;;;;;;;;;;;;;;;;;AE1C4B;AAEW;;;AAW1D,MAAO1G,4BAA4B;EAGvCmE,YACSuL,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;IAHR,KAAAuI,aAAa,GAAGD,kEAAW,CAACC,aAAa;EAGZ;EAErCpT,QAAQA,CAAA,GAER;EAEAqT,SAASA,CAAA;IACP,MAAM9O,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;IACnD;IACA,MAAM3K,GAAG,GAAG,sGAAsG,IAAI,CAACmV,aAAa,2CAA2C7O,SAAS,EAAE;IAC1L+O,KAAK,EAAEtN,MAAM,CAACrF,IAAI,CAAC1C,GAAG,EAAE,QAAQ,CAAC;EACnC;EAAC,QAAA/D,CAAA;qBAfUiB,4BAA4B,EAAA6B,+DAAA,CAAAnB,mEAAA;EAAA;EAAA,QAAA1B,EAAA;UAA5BgB,4BAA4B;IAAA+F,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAA2Q,sCAAAlS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZnBrE,4DAFtB,aAAuB,aACE,WACH,QAAG;QAAAA,oDAAA,uEAAgE;QACvFA,0DADuF,EAAI,EAAI,EACzF;QAGJA,4DADF,aAAqB,QAChB;QAAAA,oDAAA,4NAA6K;QAClLA,0DADkL,EAAI,EAChL;QAENA,4DAAA,gBAAyF;QAAtBA,wDAAA,mBAAAwW,8DAAA;UAAA,OAASlS,GAAA,CAAA+R,SAAA,EAAW;QAAA,EAAC;QAACrW,oDAAA,yBAAkB;QAC7GA,0DAD6G,EAAS,EAChH;;;mBDDFkG,+DAAS;IAAArB,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AEJuD;AACY;AAClB;AACiE;AAI9E;AAGe;AACpB;AACC;;;;;;;;;;;;;;;;;ICQrC7E,4DADF,kBAAwG,WACN;IAAAA,oDAAA,GAAqB;;IACvHA,0DADuH,EAAI,EAChH;;;;;IAF8BA,wDAAA,YAAAA,6DAAA,IAAA6W,GAAA,EAAAvW,MAAA,CAAAwW,UAAA,kBAAAxW,MAAA,CAAAwW,UAAA,CAAAhH,UAAA,CAAAhB,QAAA,CAAAiI,MAAA,GAA8D;IAClG/W,uDAAA,EAAkE;IAAlEA,wDAAA,YAAAA,6DAAA,IAAAgX,GAAA,EAAA1W,MAAA,CAAAwW,UAAA,kBAAAxW,MAAA,CAAAwW,UAAA,CAAAhH,UAAA,CAAAhB,QAAA,CAAAiI,MAAA,GAAkE;IAA2B/W,uDAAA,EAAqB;IAArBA,+DAAA,CAAAA,yDAAA,OAAA+W,MAAA,EAAqB;;;;;IAMzH/W,uDAAA,2BAA8D;;;;;;IAa1DA,4DAAA,iBAAwF;IAAtBA,wDAAA,mBAAAkX,sGAAA;MAAAlX,2DAAA,CAAAmX,GAAA;MAAA,MAAA7W,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA8W,SAAA,EAAW;IAAA,EAAC;IAACpX,oDAAA,aAAM;IAAAA,0DAAA,EAAS;;;;;;IAVzGA,4DAAA,gBAIuC;IAD/BA,wDAAA,mBAAAqX,wFAAA;MAAArX,2DAAA,CAAAuB,GAAA;MAAA,MAAAjB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAgX,WAAA,EAAa;IAAA,EAAC;IAE7BtX,uDAAA,kBAAwC;IACxCA,oDAAA,0BACF;IAAAA,0DAAA,EAAS;IAETA,wDAAA,IAAAuX,6EAAA,qBAA6B;IAG7BvX,4DAAA,iBAAyE;IAAvBA,wDAAA,mBAAAwX,wFAAA;MAAAxX,2DAAA,CAAAuB,GAAA;MAAA,MAAAjB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAmX,UAAA,EAAY;IAAA,EAAC;IAACzX,oDAAA,WAAI;IAAAA,0DAAA,EAAS;;;;IAR9EA,wDAAA,cAAAM,MAAA,CAAAoX,gBAAA,CAA8B;IAKtC1X,uDAAA,GAEC;IAFDA,2DAAA,IAAAM,MAAA,CAAAqX,SAAA,mBAEC;;;;;;IAGD3X,4DAAA,iBAAwF;IAAtBA,wDAAA,mBAAA4X,wFAAA;MAAA5X,2DAAA,CAAAoC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAuX,SAAA,EAAW;IAAA,EAAC;IAAC7X,oDAAA,aAAM;IAAAA,0DAAA,EAAS;;;;;IADvGA,wDAdF,IAAA8X,+DAAA,OAA+B,IAAAC,+DAAA,OActB;;;;IAdT/X,2DAAA,IAAAM,MAAA,CAAA0X,WAAA,kBAgBC;;;ADGC,MAAOja,yBAAyB;EAQpCuE,YACUM,QAAqB,EACrB5B,MAAc,EACf6M,OAA0B,EACzB/I,KAAqB,EACrB4O,cAA8B;IAJ9B,KAAA9Q,QAAQ,GAARA,QAAQ;IACR,KAAA5B,MAAM,GAANA,MAAM;IACP,KAAA6M,OAAO,GAAPA,OAAO;IACN,KAAA/I,KAAK,GAALA,KAAK;IACL,KAAA4O,cAAc,GAAdA,cAAc;IAXxB,KAAAuE,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAG9D,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAR,gBAAgB,GAAkB,IAAI;EAQnC;EAEH1U,QAAQA,CAAA;IACN,IAAI,CAAC8T,UAAU,GAAG,IAAI,CAACjJ,OAAO,CAACsK,aAAa,EAAE;IAC9C,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAd,WAAWA,CAAA;IACT,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB1O,MAAM,CAACrF,IAAI,CAAC,IAAI,CAAC+T,gBAAgB,EAAE,QAAQ,EAAE,qBAAqB,CAAC;IACrE,CAAC,MAAM;MACL,IAAI,CAAC9U,QAAQ,CAACe,IAAI,CAAC,gCAAgC,EAAE,QAAQ,EAAE;QAC7DC,QAAQ,EAAE,IAAI;QACdyU,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;IACJ;EACF;EAEAR,SAASA,CAAA;IACP,MAAMtQ,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;IAEnD,IAAIrE,SAAS,KAAK,IAAI,EAAE;MACtB,MAAMC,EAAE,GAAG0G,MAAM,CAAC3G,SAAS,CAAC;MAC5B,IAAI,CAACsG,OAAO,CAACyK,gBAAgB,CAAC,IAAI,CAACxB,UAAU,CAACtP,EAAE,EAAEA,EAAE,CAAC,CAACvE,SAAS,CAAC;QAC9DK,IAAI,EAAG0R,QAAQ,IAAI;UACjB,IAAI,CAACpS,QAAQ,CAACe,IAAI,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtDC,QAAQ,EAAE;WACX,CAAC;UAEF,IAAI,CAACsU,OAAO,GAAG,KAAK;UAEpB,IAAI,CAACE,SAAS,EAAE;QAClB,CAAC;QACDvU,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;OACD,CAAC;IACJ,CAAC,MAAM;MACLL,OAAO,CAACK,KAAK,CAAC,8CAA8C,CAAC;IAC/D;EACF;EAEA4T,UAAUA,CAAA;IACR,MAAMlQ,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;IACnD,IAAI,CAACsM,OAAO,GAAG,IAAI;IAEnB,IAAI3Q,SAAS,KAAK,IAAI,EAAE;MACtB,MAAMC,EAAE,GAAG0G,MAAM,CAAC3G,SAAS,CAAC;MAC5B,IAAI,CAACsG,OAAO,CAAC0K,iBAAiB,CAAC,IAAI,CAACzB,UAAU,CAACtP,EAAE,EAAEA,EAAE,CAAC,CAACvE,SAAS,CAAC;QAC/DK,IAAI,EAAGkV,QAAQ,IAAI;UACjB,IAAI,CAAC5V,QAAQ,CAACe,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpDC,QAAQ,EAAE;WACX,CAAC;UACF,IAAI,CAACsU,OAAO,GAAG,KAAK;UAEpB,IAAI,CAACE,SAAS,EAAE;QAClB,CAAC;QACDvU,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACqU,OAAO,GAAG,KAAK;UACpB,IAAI,CAACtV,QAAQ,CAACe,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACtE;OACD,CAAC;IACJ,CAAC,MAAM;MACLJ,OAAO,CAACK,KAAK,CAAC,8CAA8C,CAAC;IAC/D;EACF;EAEAuT,SAASA,CAAA;IACP,IAAI,IAAI,CAACN,UAAU,EAAE;MACnB,IAAI,CAAC9V,MAAM,CAAC8C,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BkB,WAAW,EAAE;UAAEwC,EAAE,EAAE,IAAI,CAACsP,UAAU,CAACtP;QAAE;OACtC,CAAC;IACJ;EACF;EAEA4Q,SAASA,CAAA;IACP,MAAMK,OAAO,GAAG,IAAI,CAAC3T,KAAK,CAAC4T,QAAQ,CAACC,QAAQ,CAAC3N,GAAG,CAAC,SAAS,CAAC;IAC3D,MAAM4N,QAAQ,GAAGH,OAAO,GAAGvK,MAAM,CAACuK,OAAO,CAAC,GAAG,IAAI;IAEjD,IAAIG,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAAC/K,OAAO,CAACgL,eAAe,CAACD,QAAQ,CAAC,CAAC3V,SAAS,CAAC;QAC/CK,IAAI,EAAG0R,QAAQ,IAAI;UACjBxR,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEmR,QAAQ,CAAC;UACtD,MAAMC,gBAAgB,GAAG,IAAI,CAACpH,OAAO,CAACqH,iBAAiB,CAACF,QAAQ,CAAC;UACjE,IAAI,CAAC8B,UAAU,GAAG7B,gBAAgB;UAClC,IAAI,CAACyC,gBAAgB,GAAG,IAAI,CAACZ,UAAU,CAACY,gBAAgB,CAAC,CAAC;UAE1DlU,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEoR,gBAAgB,CAAC;UAE7D,MAAM1N,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;UACnD,MAAMpE,EAAE,GAAG0G,MAAM,CAAC3G,SAAS,CAAC;UAE5B,IAAI,IAAI,CAACuP,UAAU,IAAIpK,KAAK,CAACoM,OAAO,CAAC,IAAI,CAAChC,UAAU,CAACiC,QAAQ,CAAC,EAAE;YAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACiC,QAAQ,CAAC/I,IAAI,CACnDiJ,OAAY,IAAKA,OAAO,CAACzR,EAAE,KAAKA,EAAE,CACpC;YACD,IAAI,CAACwQ,WAAW,GAAGgB,gBAAgB;YAEnC,MAAME,OAAO,GAAG,IAAI,CAACpC,UAAU,CAAChC,OAAO,KAAKtN,EAAE;YAC9C,IAAI,CAACmQ,SAAS,GAAGuB,OAAO;UAC1B;QACF,CAAC;QACDrV,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACnC;OACD,CAAC;IACJ;EACF;EAAC,QAAA5G,CAAA;qBA1HUa,yBAAyB,EAAAiC,+DAAA,CAAAnB,oEAAA,GAAAmB,+DAAA,CAAAsF,mDAAA,GAAAtF,+DAAA,CAAAgS,mEAAA,GAAAhS,+DAAA,CAAAsF,2DAAA,GAAAtF,+DAAA,CAAAkS,uDAAA;EAAA;EAAA,QAAA/U,EAAA;UAAzBY,yBAAyB;IAAAmG,SAAA;IAAAqB,UAAA;IAAAC,QAAA,GAAAxF,gEAAA,CAFzB,CAACuT,uDAAc,CAAC,GAAAvT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAwT,mCAAA/U,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrC3BrE,4DADF,aAA2B,wBACM;QAAAA,oDAAA,GAAkD;QAAAA,0DAAA,EAAiB;QAClGA,4DAAA,2BAAqC;QAAAA,oDAAA,GAAkC;QAAAA,0DAAA,EAAoB;QAGtFA,4DADL,aAA8B,QACzB,aAAQ;QAAAA,oDAAA,cAAO;QAAAA,0DAAA,EAAS;QAACA,oDAAA,GAA8B;QAAAA,0DAAA,EAAI;QAC3DA,4DAAH,SAAG,cAAQ;QAAAA,oDAAA,4BAAe;QAAAA,0DAAA,EAAS;QAACA,oDAAA,IAAsB;QAC5DA,0DAD4D,EAAI,EAC1D;QAGDA,4DADL,WAAK,SACA,cAAQ;QAAAA,oDAAA,sBAAc;QAASA,0DAAT,EAAS,EAAI;QACtCA,4DAAA,oBAAc;QACZA,wDAAA,KAAAqZ,8CAAA,sBAAwG;QAI5GrZ,0DADE,EAAe,EACX;QAIJA,wDAFF,KAAAsZ,iDAAA,+BAAsB,KAAAC,iDAAA,OAEb;QAmBXvZ,0DAAA,EAAM;QAENA,uDAAA,eAAW;;;QAxCsBA,uDAAA,GAAkD;QAAlDA,+DAAA,EAAAsE,GAAA,CAAAwS,UAAA,kBAAAxS,GAAA,CAAAwS,UAAA,CAAApJ,IAAA,aAAApJ,GAAA,CAAAwS,UAAA,kBAAAxS,GAAA,CAAAwS,UAAA,CAAArU,KAAA,EAAkD;QAC5CzC,uDAAA,GAAkC;QAAlCA,+DAAA,CAAAsE,GAAA,CAAAwS,UAAA,kBAAAxS,GAAA,CAAAwS,UAAA,CAAA0C,gBAAA,CAAkC;QAGzCxZ,uDAAA,GAA8B;QAA9BA,gEAAA,MAAAsE,GAAA,CAAAwS,UAAA,kBAAAxS,GAAA,CAAAwS,UAAA,CAAA2C,YAAA,KAA8B;QACtBzZ,uDAAA,GAAsB;QAAtBA,gEAAA,MAAAsE,GAAA,CAAAwS,UAAA,kBAAAxS,GAAA,CAAAwS,UAAA,CAAA3G,IAAA,KAAsB;QAM9BnQ,uDAAA,GAAa;QAAbA,wDAAA,YAAAsE,GAAA,CAAA2T,UAAA,CAAa;QAM3CjY,uDAAA,EAoBC;QApBDA,2DAAA,KAAAsE,GAAA,CAAA4T,OAAA,oBAoBC;;;mBDxBCnd,yDAAY,EAAAoX,oDAAA,EAAAA,oDAAA,EAAAA,0DAAA,EAIZ3L,gEAAY,EACZmQ,mEAAe,EAEfF,+DAAU,EAEVC,4DAAO,EAEPxQ,+DAAS,EAITzG,2DAAO,EAMP6T,uDAAW,EAAAjB,iDAAA,EACXmB,2EAAqB,EAAAwC,qEAAA;IAAAnR,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AE5CwB;AACI;AACF;AACmB;AAEf;AACqB;AACiC;AACrE;AAC+B;AAE3B;AACC;;;;;;;;;;;;;ICVrC7E,4DAAA,qBAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAFsCA,wDAAA,UAAAyN,SAAA,CAAAC,IAAA,WAAAD,SAAA,CAAAhL,KAAA,CAA4C;IAC7FzC,uDAAA,EACF;IADEA,gEAAA,OAAAyN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAAhL,KAAA,MACF;;;;;IAsDJzC,4DAAA,UAAoC;IAClCA,uDAAA,sCAE8B;IAChCA,0DAAA,EAAM;;;;IAFJA,uDAAA,EAAqB;IAArBA,wDAAA,eAAA4N,SAAA,CAAqB;;;ADzBrB,MAAOjQ,4BAA4B;EASvC2E,YACUQ,GAAsB,EACvB+K,OAA0B,EACzB7M,MAAc;IAFd,KAAA8B,GAAG,GAAHA,GAAG;IACJ,KAAA+K,OAAO,GAAPA,OAAO;IACN,KAAA7M,MAAM,GAANA,MAAM;IAXhB,KAAA8M,OAAO,GAAU,EAAE;IAEnB,KAAAC,YAAY,GAAgB,IAAIC,GAAG,EAAE;IACrC,KAAAC,YAAY,GAAW,EAAE;EAStB;EAEHjL,QAAQA,CAAA;IACN,IAAI,CAAC6K,OAAO,CAACgM,cAAc,EAAE,CAAC5W,SAAS,CAAEmL,KAAK,IAAI;MAChD,IAAI,CAACP,OAAO,CAACiM,WAAW,GAAG1L,KAAK;MAChC,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACtH,KAAK,CAACuH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAACiM,WAAW,CAACtL,MAAM,CAACK,MAAM,IAC3DA,MAAM,CAACpM,KAAK,CAACmM,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CACpG;EACH;EAEAM,YAAYA,CAAA;IACV,MAAMN,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACtH,KAAK,CAACuH,WAAW,EAAE;IAEhE;IACA,MAAM,CAACI,UAAU,EAAEC,WAAW,CAAC,GAAGR,WAAW,CAACS,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAAC;IAEnF,MAAMb,MAAM,GAAG,IAAI,CAACX,OAAO,CAACiM,WAAW,EAAEtL,MAAM,CAACK,MAAM,IACpD,IAAI,CAACS,iBAAiB,CAACT,MAAM,CAAC,IAC9B,IAAI,CAACU,YAAY,CAACV,MAAM,CAAC,KACxBA,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACE,UAAU,CAAC,IAC9CH,MAAM,CAACpM,KAAK,CAACmM,WAAW,EAAE,CAACE,QAAQ,CAACG,WAAW,CAAC,CAAC,CACnD,IAAI,EAAE;IAEP,IAAI,CAACnB,OAAO,GAAG,CAAC,GAAGU,MAAM,CAAC;IAC1B,IAAI,CAAC1L,GAAG,CAACK,aAAa,EAAE;EAC1B;EAEAqM,YAAYA,CAAA;IACV,IAAI,CAACd,KAAK,CAACC,aAAa,CAACtH,KAAK,GAAG,EAAE;IACnC,IAAI,CAACoI,IAAI,CAACd,aAAa,CAACtH,KAAK,GAAG,EAAE;IAClC,IAAI,CAACqI,UAAU,CAACC,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAG,KAAK,CAAC;IAC7D,IAAI,CAAC/M,GAAG,CAACK,aAAa,EAAE;IAExB,IAAI,CAAC0K,OAAO,CAACgM,cAAc,EAAE,CAAC5W,SAAS,CAAEmL,KAAK,IAAI;MAChD,IAAI,CAACP,OAAO,CAACiM,WAAW,GAAG1L,KAAK;MAChC,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAe,iBAAiBA,CAACT,MAAW;IAC3B,IAAI,CAACA,MAAM,CAACiB,UAAU,IAAI,IAAI,CAAC/B,YAAY,CAACgC,IAAI,KAAK,CAAC,EAAE;MACtD,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOlB,MAAM,CAACiB,UAAU,CAACE,IAAI,CAAEC,GAAW,IAAK,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAACrB,WAAW,EAAE,CAAC,CAAC;EAC1F;EAEAW,YAAYA,CAACV,MAAW;IACtB,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE;MACtB,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOY,MAAM,CAACsB,IAAI,IAAI,IAAI,CAAClC,YAAY;EACzC;EAEAmC,IAAIA,CAACH,GAAW;IACd,IAAI,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAClC,YAAY,CAACsC,MAAM,CAACJ,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAAClC,YAAY,CAACuC,GAAG,CAACL,GAAG,CAAC;IAC5B;EACF;EAEAM,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACvC,YAAY,GAAGuC,KAAK,CAACC,MAAM,CAACpJ,KAAK;EACxC;EAEA0S,cAAcA,CAAA;IACZ,IAAI,CAAC/Y,MAAM,CAAC8C,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAAC,QAAA5G,CAAA;qBAxFUS,4BAA4B,EAAAqC,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAnB,mEAAA,GAAAmB,+DAAA,CAAAsF,mDAAA;EAAA;EAAA,QAAAnI,EAAA;UAA5BQ,4BAA4B;IAAAuG,SAAA;IAAAC,SAAA,WAAA6V,mCAAA3V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;kEAOzBkJ,mEAAW;;;;;;;;;;gFATZ,CAACgG,uDAAc,CAAC,GAAAvT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAqU,sCAAA5V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QClCzBrE,4DAHN,aAA8B,aACJ,wBAC6C,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAsH;QAArBA,wDAAnB,mBAAAka,6DAAA;UAAAla,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC,mBAAA2L,6DAAA;UAAAna,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC;QAAnHxO,0DAAA,EAAsH;QACtHA,4DAAA,8BAA2D;QACzDA,wDAAA,IAAAoa,kDAAA,yBAAgG;QAIpGpa,0DADE,EAAmB,EACJ;QAEjBA,4DAAA,kBAI2B;QAAzBA,wDAAA,mBAAAqa,+DAAA;UAAAra,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAyK,YAAA,EAAc;QAAA,EAAC;QACxB/O,4DAAA,oBAAkC;QAAAA,oDAAA,cAAM;QAC1CA,0DAD0C,EAAW,EAC5C;QAETA,4DAAA,kBAIyB;QAAzBA,wDAAA,mBAAAsa,+DAAA;UAAAta,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAkL,YAAA,EAAc;QAAA,EAAC;QAACxP,oDAAA,sBACzB;QACFA,0DADE,EAAS,EACL;QAGJA,4DADF,eAA0B,kBAC8C;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAS;QAIvFA,4DAHN,uBAA8D,eACtC,sBACqB,wBACuC;QAAhDA,wDAAA,mBAAAua,qEAAArJ,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAwa,qEAAAtJ,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAya,qEAAAvJ,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA0a,qEAAAxJ,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA2a,qEAAAzJ,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA4a,qEAAA1J,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA6a,qEAAA3J,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAS6Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAnR,yDAAA,CAAEsE,GAAA,CAAA8L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACpQ,oDAAA,WAAG;QAGvFA,0DAHuF,EAAe,EACxF,EACN,EACG;QAEXA,4DAAA,kBAA0E;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAS;QAG7FA,4DAFJ,uBAA+C,eACvB,0BACmE;QAAnCA,wDAAA,mBAAA8a,uEAAA5J,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASkR,MAAA,CAAAC,eAAA,EAAwB;QAAA,EAAC;QACpFnR,4DAAA,iBAAW;QAAAA,oDAAA,oBAAY;QAAAA,0DAAA,EAAY;QACnCA,4DAAA,oBAAmE;QAAjCA,wDAAA,oBAAA+a,+DAAA7J,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUsE,GAAA,CAAAiM,YAAA,CAAAW,MAAA,CAAoB;QAAA,EAAC;QAIzElR,0DAJQ,EAAmE,EACpD,EACb,EACG,EACP;QAGJA,4DADF,eAAsC,kBAC4B;QAA3BA,wDAAA,mBAAAgb,+DAAA;UAAAhb,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAyV,cAAA,EAAgB;QAAA,EAAC;QAC7D/Z,4DAAA,gBAAU;QAAAA,oDAAA,WAAG;QAEjBA,0DAFiB,EAAW,EACjB,EACL;QAENA,4DAAA,eAAuB;QACrBA,wDAAA,KAAAib,4CAAA,kBAAoC;QAMxCjb,0DADE,EAAM,EACF;QAENA,uDAAA,eAAW;;;;;;QAlEgDA,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA6R,OAAA,CAAwB;QAE5C7R,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAsE,GAAA,CAAAgK,eAAA,CAAkB;QAuBlCtO,uDAAA,GAA0B;QAA1BA,wDAAA,sBAAA8R,OAAA,CAA0B;QAe1B9R,uDAAA,IAA8B;QAA9BA,wDAAA,sBAAA+R,WAAA,CAA8B;QAkBzB/R,uDAAA,IAAU;QAAVA,wDAAA,YAAAsE,GAAA,CAAAwJ,OAAA,CAAU;;;mBDtC9BzH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACTzG,2DAAO,EACPzD,kEAAa,EAAAgW,4DAAA,EAAAA,mEAAA,EACbzW,yEAAiB,EAAA2W,mEAAA,EAEjB5E,mDAAK,EACLE,qHAA6B,EAC7BrS,kFAAqB,EAAAgX,4EAAA,EAAAE,8DAAA,EAAAF,mFAAA,EACrBmB,uDAAW,EAAA0C,iDAAA;IAAAnR,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;AElC0C;AAGR;AACE;AACO;AACiE;;;;;;;ICkBtH7E,4DADD,kBAAqE,WAC5C;IAAAA,oDAAA,GAAmB;;IAC5CA,0DAD4C,EAAI,EACrC;;;;IADcA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAA,yDAAA,OAAA+W,MAAA,EAAmB;;;ADM9C,MAAOvJ,6BAA6B;EAGxClL,YACSuL,OAA0B,EACzB7M,MAAc;IADf,KAAA6M,OAAO,GAAPA,OAAO;IACN,KAAA7M,MAAM,GAANA,MAAM;EAAW;EAE3Boa,iBAAiBA,CAACtE,UAAe;IAC/B,IAAI,CAACjJ,OAAO,CAACsH,aAAa,CAAC2B,UAAU,CAAC;IACtC,IAAI,CAAC9V,MAAM,CAAC8C,QAAQ,CAAC,CAAC,WAAWgT,UAAU,CAACtP,EAAE,EAAE,CAAC,CAAC;IAElD;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAAC,QAAAtK,CAAA;qBAlBUsQ,6BAA6B,EAAAxN,+DAAA,CAAAnB,mEAAA,GAAAmB,+DAAA,CAAAsF,mDAAA;EAAA;EAAA,QAAAnI,EAAA;UAA7BqQ,6BAA6B;IAAAtJ,SAAA;IAAAmX,MAAA;MAAAvE,UAAA;IAAA;IAAAvR,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAA0V,uCAAAjX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC5BpCrE,4DAHN,aAAuB,eACX,sBACS,wBACkF;QAAAA,oDAAA,GAAsB;QAAAA,0DAAA,EAAiB;QACxIA,4DAAA,2BAA2F;QAAAA,oDAAA,GAAiC;QAC9HA,0DAD8H,EAAoB,EAChI;QAOdA,4DANJ,uBAAkB,QAKb,aACO;QAAAA,oDAAA,eAAO;QAAAA,0DAAA,EAAS;QACxBA,oDAAA,IACF;QAAAA,0DAAA,EAAI;QAEFA,4DADF,SAAG,cACO;QAAAA,oDAAA,mBAAW;QAAAA,0DAAA,EAAS;QAC5BA,oDAAA,IACF;;QAAAA,0DAAA,EAAI;QAEFA,4DADF,SAAG,cACO;QAAAA,oDAAA,4BAAe;QAAAA,0DAAA,EAAS;QAChCA,oDAAA,IACF;QAAAA,0DAAA,EAAI;QACJA,4DAAA,oBAAc;QACZA,wDAAA,KAAAub,kDAAA,sBAAqE;QAIzEvb,0DADE,EAAe,EACE;QAGjBA,4DADF,wBAAkB,iBAK0B;QAAxCA,wDAAA,mBAAAwb,gEAAA;UAAA,OAASlX,GAAA,CAAA8W,iBAAA,CAAA9W,GAAA,CAAAwS,UAAA,CAA6B;QAAA,EAAC;QACvC9W,oDAAA,kBACF;QAGNA,0DAHM,EAAS,EACQ,EACV,EACP;;;QArC6DA,uDAAA,GAAmC;QAAnCA,mEAAA,eAAAsE,GAAA,CAAAwS,UAAA,CAAArU,KAAA,CAAmC;QAACzC,uDAAA,EAAsB;QAAtBA,+DAAA,CAAAsE,GAAA,CAAAwS,UAAA,CAAArU,KAAA,CAAsB;QAC3EzC,uDAAA,EAA8C;QAA9CA,mEAAA,eAAAsE,GAAA,CAAAwS,UAAA,CAAA0C,gBAAA,CAA8C;QAACxZ,uDAAA,EAAiC;QAAjCA,+DAAA,CAAAsE,GAAA,CAAAwS,UAAA,CAAA0C,gBAAA,CAAiC;QAS1HxZ,uDAAA,GACF;QADEA,gEAAA,MAAAsE,GAAA,CAAAwS,UAAA,CAAA2C,YAAA,MACF;QAGEzZ,uDAAA,GACF;QADEA,gEAAA,MAAAA,yDAAA,QAAAsE,GAAA,CAAAwS,UAAA,CAAAxC,QAAA,OACF;QAGEtU,uDAAA,GACF;QADEA,gEAAA,MAAAsE,GAAA,CAAAwS,UAAA,CAAA3G,IAAA,MACF;QAE6CnQ,uDAAA,GAAwB;QAAxBA,wDAAA,YAAAsE,GAAA,CAAAwS,UAAA,CAAAhH,UAAA,CAAwB;;;mBDRnEvJ,2DAAO,EACP2U,iEAAa,EACb1U,gEAAY,EACZmQ,mEAAe,EACflQ,kEAAc,EACdgQ,+DAAU,EACVnJ,kDAAK,EACLoJ,4DAAO,EACPyE,kEAAc,EACdjV,+DAAS,EAETyT,0DAAa,EACb5c,uEAAgB,EAAAiV,iEAAA;IAAAnN,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;AE5BoC;AACT;AACY;;AAI/D,MAAMyF,QAAQ,GAAG,uBAAuB;AAExC,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,IAAIN,6DAAW,CAAC;IAAE,cAAc,EAAE;EAAkB,CAAE;CAChE;AAKK,MAAOwG,iBAAiB;EAO5BpO,YAAA;IALA,KAAAwX,WAAW,GAAiB,EAAE;IAC9B,KAAAzL,aAAa,GAAiB,EAAE;IAChC,KAAAoG,QAAQ,GAAU,EAAE;IAEH,KAAAhK,IAAI,GAAG1L,qDAAM,CAACkL,4DAAU,CAAC;EAC3B;EAEf4P,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpP,IAAI,CACbE,IAAI,CAAQ,GAAGL,QAAQ,sBAAsB,EAAE,EAAE,EAAEC,WAAW,CAAC,CAC/DM,IAAI,CACHsE,yCAAG,CAAE2K,WAAW,IAAKA,WAAW,CAAC3K,GAAG,CAAC,IAAI,CAAC+F,iBAAiB,CAAC,CAAC,EAC7D/K,gDAAU,CAAEtG,KAAK,IAAI;MACnBL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOsH,wCAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEAgD,kBAAkBA,CAACwN,SAAiB;IAClC,MAAMC,WAAW,GAAG;MAAED;IAAS,CAAE;IAEjC,OAAO,IAAI,CAAClR,IAAI,CAACE,IAAI,CAAQ,GAAGL,QAAQ,sBAAsB,EAAEsR,WAAW,EAAErR,WAAW,CAAC,CACxFM,IAAI,CACHsE,yCAAG,CAAE2K,WAAW,IAAKA,WAAW,CAAC3K,GAAG,CAAC,IAAI,CAAC+F,iBAAiB,CAAC,CAAC,EAC7D/K,gDAAU,CAAEtG,KAAK,IAAI;MACnBL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOsH,wCAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;IAAC;EACJ;EAEO+J,iBAAiBA,CAAC2G,IAAS;IAChC,MAAM;MACJrU,EAAE;MACFyM,WAAW;MACX6H,KAAK;MACL3H,OAAO;MACP4E,QAAQ;MACR3E,WAAW;MACXC,WAAW;MACXC,QAAQ;MACRC,QAAQ;MACRO,OAAO;MACP4C;IAAgB,CACjB,GAAGmE,IAAI;IAER;IACA;IAEA,IAAIE,UAAU,GAAG,CAAC;IAClB,IAAID,KAAK,EAAE;MACTC,UAAU,GAAG,CAAC;IAChB;IACA,MAAMC,WAAW,GAAG,GAAGD,UAAU,IAAI,CAAC,EAAE;IAExC,MAAME,iBAAiB,GAAGlD,QAAQ,CAACnM,MAAM;IACzC,MAAMsP,gBAAgB,GAAG,GAAGD,iBAAiB,IAAI7H,WAAW,EAAE;IAC9D,MAAM+H,UAAU,GAAG9H,WAAW,CAAC+H,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAE3C,IAAIC,cAAc,GAAG,EAAE;IACvB,QAAQ/H,QAAQ;MACd,KAAK,QAAQ;QACX+H,cAAc,GAAG,QAAQ;QACzB;MACF,KAAK,YAAY;QACfA,cAAc,GAAG,YAAY;QAC7B;MACF;QACEA,cAAc,GAAG,SAAS;IAC9B;IAEA,OAAO;MACL7U,EAAE,EAAEA,EAAE;MACN/E,KAAK,EAAE0R,OAAO,CAAC9S,IAAI;MACnBqM,IAAI,EAAEyG,OAAO,CAACzG,IAAI;MAClBoH,OAAO,EAAEA,OAAO;MAChB0E,gBAAgB,EAAEvF,WAAW;MAC7BK,QAAQ,EAAE+H,cAAc;MACxBlM,IAAI,EAAEgM,UAAU;MAChBG,OAAO,EAAEN,WAAW;MACpBvC,YAAY,EAAEyC,gBAAgB;MAC9BnD,QAAQ,EAAEA,QAAQ;MAClBjJ,UAAU,EAAEyE,QAAQ,CAACpF,GAAG,CAAEc,GAAqB,IAC7CA,GAAG,CAAC5O,IAAI,CAACuN,WAAW,EAAE,CAAC2N,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACvC;MACD7E,gBAAgB,EAAEA;KACnB;EACH;EAEAY,gBAAgBA,CAACkE,OAAe,EAAChV,EAAU;IACzC,OAAO,IAAI,CAACiD,IAAI,CAACE,IAAI,CAAQ,GAAGL,QAAQ,iBAAiBkS,OAAO,aAAahV,EAAE,OAAO,EAAE,EAAE,EAAE+C,WAAW,CAAC;EAC1G;EAEAgO,iBAAiBA,CAACiE,OAAe,EAAChV,EAAU;IAC1C,OAAO,IAAI,CAACiD,IAAI,CAACE,IAAI,CAAQ,GAAGL,QAAQ,iBAAiBkS,OAAO,aAAahV,EAAE,QAAQ,EAAE,EAAE,EAAE+C,WAAW,CAAC;EAC3G;EAEAiK,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC/J,IAAI,CAACO,GAAG,CAAQ,GAAGV,QAAQ,WAAW,CAAC;EACrD;EAEAuO,eAAeA,CAAC4D,YAAoB;IAClC,OAAO,IAAI,CAAChS,IAAI,CAACO,GAAG,CAAQ,GAAGV,QAAQ,iBAAiBmS,YAAY,EAAE,CAAC;EACzE;EAEA1H,gBAAgBA,CAACF,cAAmB;IAClC,OAAO,IAAI,CAACpK,IAAI,CAACE,IAAI,CAAM,GAAGL,QAAQ,eAAe,EAAEuK,cAAc,CAAC;EACxE;EAEA6H,cAAcA,CAAC7H,cAAmB,EAAE2H,OAAe;IACjD,OAAO,IAAI,CAAC/R,IAAI,CAACkS,GAAG,CAAM,GAAGrS,QAAQ,iBAAiBkS,OAAO,EAAE,EAAE3H,cAAc,CAAC;EAClF;EAEAM,aAAaA,CAAC5R,IAAS;IACrB,IAAI,CAACqZ,eAAe,GAAGrZ,IAAI;EAC7B;EAEA4U,aAAaA,CAAA;IACX,OAAO,IAAI,CAACyE,eAAe;EAC7B;EAAC,QAAA1f,CAAA;qBA1HUwT,iBAAiB;EAAA;EAAA,QAAAvT,EAAA;WAAjBuT,iBAAiB;IAAA/G,OAAA,EAAjB+G,iBAAiB,CAAA9G,IAAA;IAAAqB,UAAA,EAFhB;EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbkC;AAEkD;AACjC;AACH;AACI;AACrB;AACmB;AAEnB;AACE;AAIW;AACpB;AACC;;;;;;;;;;;;;;ICTnCjL,4DAAA,qBAAoE;IAElEA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAHsCA,wDAAA,UAAAyN,SAAA,CAAgB;IAEjEzN,uDAAA,EACF;IADEA,gEAAA,OAAAyN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAApM,IAAA,MACF;;;;;IA8BJrB,4DAAA,0BAA8D;IAC5DA,oDAAA,GACF;IAAAA,0DAAA,EAAkB;;;;IAF8BA,wDAAA,UAAAyT,MAAA,CAAa;IAC3DzT,uDAAA,EACF;IADEA,gEAAA,MAAAyT,MAAA,CAAApS,IAAA,MACF;;;;;IAOErB,uDAAA,4BAAyC;;;;;;IAEzCA,4DAAA,iBAAkF;IAAvBA,wDAAA,mBAAA6c,0EAAA;MAAA7c,2DAAA,CAAAmX,GAAA;MAAA,MAAA2F,MAAA,GAAA9c,2DAAA;MAAA,OAAAA,yDAAA,CAAS8c,MAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAAC/c,oDAAA,sBAAe;IAAAA,0DAAA,EAAS;;;ADL5G,MAAO9B,yBAAyB;EAyBpCoE,YACUM,QAAqB,EACrB8Q,cAA8B,EAC9B5O,KAAqB,EACrB9D,MAAc,EACd8B,GAAsB,EACvB+K,OAA0B,EACzB8F,OAA2B;IAN3B,KAAA/Q,QAAQ,GAARA,QAAQ;IACR,KAAA8Q,cAAc,GAAdA,cAAc;IACd,KAAA5O,KAAK,GAALA,KAAK;IACL,KAAA9D,MAAM,GAANA,MAAM;IACN,KAAA8B,GAAG,GAAHA,GAAG;IACJ,KAAA+K,OAAO,GAAPA,OAAO;IACN,KAAA8F,OAAO,GAAPA,OAAO;IA9BjB,KAAA7F,OAAO,GAAU,EAAE;IAEnB,KAAAoK,OAAO,GAAY,KAAK;IAGd,KAAApI,UAAU,GAAmC,CACrD;MAAEtI,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEmG,EAAE,EAAE,CAAC;MAAEnG,IAAI,EAAE;IAAK,CAAE,CACvB;IAES,KAAAuS,UAAU,GAA2C,CAC7D;MAAEvM,KAAK,EAAE,YAAY;MAAEwM,SAAS,EAAE;IAAY,CAAE,EAChD;MAAExM,KAAK,EAAE,QAAQ;MAAEwM,SAAS,EAAE;IAAQ,CAAE,EACxC;MAAExM,KAAK,EAAE,SAAS;MAAEwM,SAAS,EAAE;IAAS,CAAE,CAC3C;IAES,KAAAC,gBAAgB,GAAkB,IAAI;IAU9C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC9M,KAAK,CAAC;MACnCW,EAAE,EAAE,CAAC,EAAE,CAAC;MACR/E,KAAK,EAAE,CAAC,EAAE,CAAC;MACXwR,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,QAAQ,CAAC;MACpBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEAvR,QAAQA,CAAA;IACN,IAAI,CAAC6K,OAAO,CAAC2G,WAAW,EAAE,CAACvR,SAAS,CAAEmL,KAAK,IAAI;MAC7C,IAAI,CAACP,OAAO,CAAC4G,QAAQ,GAAGrG,KAAK;MAC7B,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACzJ,KAAK,CAACE,WAAW,CAAC/B,SAAS,CAACgC,MAAM,IAAG;MACxC,MAAMuX,OAAO,GAAGvX,MAAM,CAAC,IAAI,CAAC;MAE5B,IAAI,CAAC4I,OAAO,CAACgL,eAAe,CAAC2D,OAAO,CAAC,CAACvZ,SAAS,CAAEmL,KAAU,IAAI;QAC7D,IAAI,CAAC4F,UAAU,CAACW,UAAU,CAACvG,KAAK,CAAC;QACjC,IAAI,CAAC4F,UAAU,CAAChJ,GAAG,CAAC,aAAa,CAAC,EAAE2J,UAAU,CAACvG,KAAK,CAAC+F,OAAO,CAACzG,IAAI,GAAG,KAAK,GAAGU,KAAK,CAAC+F,OAAO,CAAC9S,IAAI,CAAC;QAE/F,MAAM0M,YAAY,GAAGK,KAAK,CAACmG,QAAQ,CAACpF,GAAG,CAAEc,GAAiC,IAAI;UAC5E,OAAO,IAAI,CAACH,UAAU,CAACkN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzV,EAAE,KAAKyI,GAAG,CAACzI,EAAE,CAAC;QACnD,CAAC,CAAC;QACF,IAAI,CAACwM,UAAU,CAAChJ,GAAG,CAAC,UAAU,CAAC,EAAE2J,UAAU,CAAC5G,YAAY,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAS,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACtH,KAAK,CAACuH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAAC4G,QAAQ,CAACjG,MAAM,CAACK,MAAM,IACxDA,MAAM,CAACrH,EAAE,CAACC,QAAQ,EAAE,CAACqH,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACxN,IAAI,CAACuN,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CACjJ;EACH;EAEAiG,gBAAgBA,CAAC7F,MAAW;IAC1B,IAAI,CAACmF,UAAU,CAAChJ,GAAG,CAAC,SAAS,CAAC,EAAE2J,UAAU,CAAC9F,MAAM,CAAC;IAClD,IAAI,CAACmF,UAAU,CAAChJ,GAAG,CAAC,aAAa,CAAC,EAAE2J,UAAU,CAAC9F,MAAM,CAACnB,IAAI,GAAG,KAAK,GAAGmB,MAAM,CAACxN,IAAI,CAAC;EACnF;EAEA0b,UAAUA,CAAA;IACR,MAAMxV,SAAS,GAAGG,YAAY,CAACkE,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMpE,EAAE,GAAG0G,MAAM,CAAC3G,SAAS,CAAC;IAC5B,MAAM9E,KAAK,GAAG,IAAI,CAACuR,UAAU,EAAE3M,KAAK,CAAC8M,OAAO,CAACzG,IAAI,GAAG,GAAG,GAAG,IAAI,CAACsG,UAAU,EAAE3M,KAAK,CAAC8M,OAAO,CAAC9S,IAAI;IAE7F,MAAMmb,OAAO,GAAG,IAAI,CAACxI,UAAU,EAAE3M,KAAK,CAACG,EAAE;IAEzC,MAAMqN,cAAc,GAAG;MACrBrN,EAAE,EAAE,IAAI,CAACwM,UAAU,EAAE3M,KAAK,CAACG,EAAE;MAC7B/E,KAAK,EAAEA,KAAK;MACZwR,WAAW,EAAE,IAAI,CAACD,UAAU,EAAE3M,KAAK,CAAC4M,WAAW;MAC/CiJ,MAAM,EAAE1V,EAAE;MACVsN,OAAO,EAAEtN,EAAE;MACX2M,OAAO,EAAE,IAAI,CAACH,UAAU,EAAE3M,KAAK,CAAC8M,OAAO;MACvCI,QAAQ,EAAE,IAAI,CAACP,UAAU,EAAE3M,KAAK,CAACkN,QAAQ;MACzCF,WAAW,EAAE,IAAI,CAACL,UAAU,EAAE3M,KAAK,CAACgN,WAAW;MAC/CD,WAAW,EAAE,IAAI,CAACJ,UAAU,EAAE3M,KAAK,CAAC+M,WAAW;MAC/CE,QAAQ,EAAE,IAAI,CAACN,UAAU,EAAE3M,KAAK,CAACiN;KAClC;IAED,IAAI,CAAC4D,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACrK,OAAO,CAAC6O,cAAc,CAAC7H,cAAc,EAAE2H,OAAO,CAAC,CAACvZ,SAAS,CAC5D+R,QAAQ,IAAG;MACTxR,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEuR,QAAQ,CAAC;MAE7D,IAAI,CAACpS,QAAQ,CAACe,IAAI,CAChB,sCAAsC,EACtC,GAAG,EACH;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;MACD,IAAI,CAACiK,OAAO,CAACsH,aAAa,CAACH,QAAQ,CAAC;MACpC,IAAI,CAAChU,MAAM,CAAC8C,QAAQ,CAAC,CAAC,WAAWkR,QAAQ,CAACxN,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC,EACD3D,KAAK,IAAG;MACN,IAAI,CAACqU,OAAO,GAAG,KAAK;MACpB1U,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,CACF;EACH;EAAC,QAAA3G,CAAA;qBAvHUgB,yBAAyB,EAAA8B,+DAAA,CAAAnB,oEAAA,GAAAmB,+DAAA,CAAAsF,uDAAA,GAAAtF,+DAAA,CAAAgS,2DAAA,GAAAhS,+DAAA,CAAAgS,mDAAA,GAAAhS,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAkS,mEAAA,GAAAlS,+DAAA,CAAAmS,8DAAA;EAAA;EAAA,QAAAhV,EAAA;UAAzBe,yBAAyB;IAAAgG,SAAA;IAAAC,SAAA,WAAAgZ,gCAAA9Y,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;gFAFzB,CAACkP,uDAAc,CAAC,GAAAvT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAwX,mCAAA/Y,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCxCrBrE,4DAJR,aAA8B,cACI,aACb,wBACoD,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAoJ;QAArBA,wDAAnB,mBAAAqd,0DAAA;UAAArd,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC,mBAAA8O,0DAAA;UAAAtd,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUsE,GAAA,CAAAkK,MAAA,EAAQ;QAAA,EAAC;QAAjJxO,0DAAA,EAAoJ;QACpJA,4DAAA,6BAAoH;QAAzDA,wDAAA,4BAAAud,8EAAArM,MAAA;UAAAlR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAkBsE,GAAA,CAAAoQ,gBAAA,CAAAxD,MAAA,CAAArC,MAAA,CAAAxH,KAAA,CAAqC;QAAA,EAAC;QACjHrH,wDAAA,KAAAwd,gDAAA,wBAAoE;QAM1Exd,0DAFI,EAAmB,EACJ,EACb;QAGJA,4DADF,yBAAiC,iBACpB;QAAAA,oDAAA,2BAAS;QAAAA,0DAAA,EAAY;QAChCA,uDAAA,oBAA4D;QAC9DA,0DAAA,EAAiB;QAIbA,4DAFJ,cAAiB,sBACC,iBACH;QAAAA,oDAAA,6BAAgB;QAAAA,0DAAA,EAAY;QAErCA,4DADF,sBAA0C,sBAChB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAE7BA,0DAF6B,EAAa,EAC3B,EACE;QAGfA,4DADF,0BAAuC,iBAC1B;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAY;QACrCA,uDAAA,iBAA2D;QAE/DA,0DADE,EAAiB,EACb;QAENA,4DAAA,UAAI;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAK;QACvBA,4DAAA,4BAAsD;QACpDA,wDAAA,KAAAyd,qDAAA,6BAA8D;QAGhEzd,0DAAA,EAAmB;QAEnBA,4DAAA,eAAuB;QACrBA,uDAAA,eAAW;QAITA,wDAFF,KAAA0d,iDAAA,gCAAsB,KAAAC,iDAAA,OAEb;QAKf3d,0DAFI,EAAM,EACD,EACH;;;;QAtDEA,uDAAA,EAAwB;QAAxBA,wDAAA,cAAAsE,GAAA,CAAA0P,UAAA,CAAwB;QAI2DhU,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA4d,OAAA,CAAwB;QAE1E5d,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAsE,GAAA,CAAAgK,eAAA,CAAkB;QAiBrCtO,uDAAA,IAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QAYMA,uDAAA,GAAa;QAAbA,wDAAA,YAAAsE,GAAA,CAAAwL,UAAA,CAAa;QAQ9C9P,uDAAA,GAIC;QAJDA,2DAAA,KAAAsE,GAAA,CAAA4T,OAAA,oBAIC;;;mBD7BHnd,yDAAY,EAAAsX,oDAAA,EACZpM,+DAAmB,EAAAkM,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAInB9L,sEAAY,EACZC,kEAAQ,EACR+M,+DAAS,EACTrN,uDAAW,EAEXsM,6DAAS,EACTa,oEAAc,EACdC,mEAAa,EACbhN,8DAAQ,EACRF,gEAAS,EACT/K,kFAAqB,EAAA6a,4EAAA,EAAAA,mFAAA,EACrB1C,uDAAW,EAAA2C,iDAAA,EACXzC,2EAAqB,EAAAqK,qEAAA;IAAAhZ,MAAA;EAAA;;;;;;;;;;;;;;;AExClB,MAAMsR,WAAW,GAAG;EACvB2H,WAAW,EAAE,IAAI;EACjB1H,aAAa,EAAE,+BAA+B;EAC9C;EACA2H,OAAO,EAAE;CACZ;;;;;;;;;;;;;;;;;;;;;;ACFmD;AACD;AACO;AACH;AACsB;AACI;AACf;AACc;AACV;AACV;AACmB;AAG/EM,+EAAoB,CAAChc,4DAAY,EAAE;EAC/Bic,SAAS,EAAE,CACPN,kEAAmB,CAACrf,qEAAgB,EAAE1B,+EAAqB,EAAEmhB,oEAAa,EAAEpY,uDAAW,EAAEC,+DAAmB,EAAE3K,iEAAa,EAAE+R,mEAAY,CAAC,EAC1IxD,wFAAwB,EACxBsU,uEAAiB,CAACD,4EAAsB,EAAE,CAAC,EAC3CD,4FAAqB,EAAE;CAE9B,CAAC,CACCjL,KAAK,CAAC5N,GAAG,IAAI5B,OAAO,CAACK,KAAK,CAACuB,GAAG,CAAC,CAAC", "sources": ["./src/app/angular-material.module.ts", "./src/app/app-routing.module.ts", "./src/app/app.component.ts", "./src/app/app.component.html", "./src/app/auth/components/email-confirm/email-confirm.component.ts", "./src/app/auth/components/email-confirm/email-confirm.component.html", "./src/app/auth/components/login/login.component.ts", "./src/app/auth/components/login/login.component.html", "./src/app/auth/components/password-recovery/password-recovery.component.ts", "./src/app/auth/components/password-recovery/password-recovery.component.html", "./src/app/auth/components/register/register.component.ts", "./src/app/auth/components/register/register.component.html", "./src/app/core/helpers/http.interceptor.ts", "./src/app/core/security/auth/auth.service.ts", "./src/app/core/security/auth/storage.service.ts", "./src/app/core/security/guard/auth.guard.ts", "./src/app/shared/components/header/header.component.ts", "./src/app/shared/components/header/header.component.html", "./src/app/shared/shared.module.ts", "./src/app/study-group/my-study-group/my-study-group.component.ts", "./src/app/study-group/my-study-group/my-study-group.component.html", "./src/app/study-group/navigation-service.service.ts", "./src/app/study-group/study-create-group/study-create-group.component.ts", "./src/app/study-group/study-create-group/study-create-group.component.html", "./src/app/study-group/study-group-associate/study-group-associate.component.ts", "./src/app/study-group/study-group-associate/study-group-associate.component.html", "./src/app/study-group/study-group-detail/study-group-detail.component.ts", "./src/app/study-group/study-group-detail/study-group-detail.component.html", "./src/app/study-group/study-group-search-bar/study-group-search.component.ts", "./src/app/study-group/study-group-search-bar/study-group-search-bar.component.html", "./src/app/study-group/study-group-search-item/study-group-search-item.component.ts", "./src/app/study-group/study-group-search-item/study-group-search-item.component.html", "./src/app/study-group/study-group.service.ts", "./src/app/study-group/study-update-group/study-update-group.component.ts", "./src/app/study-group/study-update-group/study-update-group.component.html", "./src/environments/environment.development.ts", "./src/main.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common'\r\nimport { NgModule } from '@angular/core'\r\nimport { OverlayModule } from '@angular/cdk/overlay'\r\nimport { CdkTreeModule } from '@angular/cdk/tree'\r\nimport { PortalModule } from '@angular/cdk/portal'\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete'\r\nimport { MatButtonModule } from '@angular/material/button'\r\nimport { MatButtonToggleModule } from '@angular/material/button-toggle'\r\nimport { MatCardModule } from '@angular/material/card'\r\nimport { MatCheckboxModule } from '@angular/material/checkbox'\r\nimport { MatChipsModule } from '@angular/material/chips'\r\nimport { MatRippleModule } from '@angular/material/core'\r\nimport { MatDividerModule } from '@angular/material/divider'\r\nimport { MatExpansionModule } from '@angular/material/expansion'\r\nimport { MatFormFieldModule } from '@angular/material/form-field'\r\nimport { MatIconModule } from '@angular/material/icon'\r\nimport { MatInputModule } from '@angular/material/input'\r\nimport { MatListModule } from '@angular/material/list'\r\nimport { MatMenuModule } from '@angular/material/menu'\r\nimport { MatPaginatorModule } from '@angular/material/paginator'\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner'\r\nimport { MatSelectModule } from '@angular/material/select'\r\nimport { MatSidenavModule } from '@angular/material/sidenav'\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar'\r\nimport { MatSortModule } from '@angular/material/sort'\r\nimport { MatTableModule } from '@angular/material/table'\r\nimport { MatTabsModule } from '@angular/material/tabs'\r\nimport { MatToolbarModule } from '@angular/material/toolbar'\r\nimport { MatTreeModule } from '@angular/material/tree'\r\nimport { MatBadgeModule } from '@angular/material/badge'\r\nimport { MatGridListModule } from '@angular/material/grid-list'\r\nimport { MatRadioModule } from '@angular/material/radio'\r\nimport { MatDatepickerModule } from '@angular/material/datepicker'\r\nimport { MatTooltipModule } from '@angular/material/tooltip'\r\n\r\nconst materialModules = [\r\n  CdkTreeModule,\r\n  MatAutocompleteModule,\r\n  MatButtonModule,\r\n  MatCardModule,\r\n  MatCheckboxModule,\r\n  MatChipsModule,\r\n  MatDividerModule,\r\n  MatExpansionModule,\r\n  MatIconModule,\r\n  MatInputModule,\r\n  MatListModule,\r\n  MatMenuModule,\r\n  MatProgressSpinnerModule,\r\n  MatPaginatorModule,\r\n  MatRippleModule,\r\n  MatSelectModule,\r\n  MatSidenavModule,\r\n  MatSnackBarModule,\r\n  MatSortModule,\r\n  MatTableModule,\r\n  MatTabsModule,\r\n  MatToolbarModule,\r\n  MatFormFieldModule,\r\n  MatButtonToggleModule,\r\n  MatTreeModule,\r\n  OverlayModule,\r\n  PortalModule,\r\n  MatBadgeModule,\r\n  MatGridListModule,\r\n  MatRadioModule,\r\n  MatDatepickerModule,\r\n  MatTooltipModule\r\n]\r\n\r\n@NgModule({\r\n  imports: [CommonModule, ...materialModules],\r\n  exports: [...materialModules]\r\n})\r\nexport class AngularMaterialModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { RegisterComponent } from './auth/components/register/register.component';\r\nimport { LoginComponent } from './auth/components/login/login.component';\r\nimport { EmailConfirmComponent } from './auth/components/email-confirm/email-confirm.component';\r\nimport { StudyGroupSearchBarComponent } from './study-group/study-group-search-bar/study-group-search.component';\r\nimport { PasswordRecoveryComponent } from './auth/components/password-recovery/password-recovery.component';\r\nimport { authGuard, discordAssociateGuard, loggedInGuard } from './core/security/guard/auth.guard';\r\nimport { StudyGroupDetailComponent } from './study-group/study-group-detail/study-group-detail.component';\r\nimport { StudyCreateGroupComponent } from './study-group/study-create-group/study-create-group.component';\r\nimport { MyStudyGroupComponent } from './study-group/my-study-group/my-study-group.component';\r\nimport { StudyUpdateGroupComponent } from './study-group/study-update-group/study-update-group.component';\r\nimport { StudyGroupAssociateComponent } from './study-group/study-group-associate/study-group-associate.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'password-recovery',\r\n    component: PasswordRecoveryComponent,\r\n    canActivate: [loggedInGuard],\r\n  },\r\n  { path: 'login', component: LoginComponent, canActivate: [loggedInGuard] },\r\n  // prettier-ignore\r\n  { path: 'register', component: RegisterComponent, canActivate: [loggedInGuard]},\r\n  // prettier-ignore\r\n  { path: 'confirm', component: EmailConfirmComponent, canActivate: [loggedInGuard] },\r\n  {\r\n    path: '',\r\n    component: LoginComponent,\r\n    canActivate: [loggedInGuard],\r\n  },\r\n  {\r\n    path: '',\r\n    canActivate: [authGuard],\r\n    children: [\r\n      { path: 'search', component: StudyGroupSearchBarComponent },\r\n      { path: 'create', component: StudyCreateGroupComponent },\r\n      { path: 'detail/:groupId', component: StudyGroupDetailComponent },\r\n      { path: 'my-study-group', component: MyStudyGroupComponent },\r\n      { path: 'edit', component: StudyUpdateGroupComponent },\r\n      { path: 'associate', component: StudyGroupAssociateComponent },\r\n      { path: '', redirectTo: 'search', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n", "import { ChangeDetectorRef, Component, OnInit, ViewChild, inject } from '@angular/core';\r\nimport { AuthService } from './core/security/auth/auth.service';\r\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\r\nimport { UserResponseBasicDto } from './shared/models/user/user-response-basic-dto';\r\nimport { MatNavList, MatListItem } from '@angular/material/list';\r\nimport { MatSidenavContainer, MatSidenav, MatSidenavContent } from '@angular/material/sidenav';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatIconButton } from '@angular/material/button';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Location } from '@angular/common'\r\nimport { NavigationServiceService } from './study-group/navigation-service.service';\r\n\r\n@Component({\r\n    selector: 'app-root',\r\n    templateUrl: './app.component.html',\r\n    styleUrls: ['./app.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        MatToolbar,\r\n        MatIconButton,\r\n        MatIcon,\r\n        MatSidenavContainer,\r\n        MatSidenav,\r\n        MatNavList,\r\n        MatListItem,\r\n        RouterLink,\r\n        MatSidenavContent,\r\n        RouterOutlet,\r\n        MatMenu,\r\n        MatMenuItem,\r\n        MatMenuTrigger,\r\n    ],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  appName: string = 'Bora Estudar UFF';\r\n  title = 'bora-estudar-front';\r\n  isLoggedIn = false;\r\n  user: UserResponseBasicDto | undefined = undefined;\r\n  showBackIcon = false;\r\n  @ViewChild('snav') sidenav!: MatSidenav;\r\n\r\n  private snackBar = inject(MatSnackBar);\r\n  private authService = inject(AuthService);\r\n  public router = inject(Router);\r\n  private cdr = inject(ChangeDetectorRef);\r\n  private navigationService = inject(NavigationServiceService);\r\n\r\n  constructor(\r\n    private location: Location\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.isLoggedIn().subscribe((isLoggedIn) => {\r\n      this.isLoggedIn = isLoggedIn;\r\n      if (isLoggedIn) {\r\n        this.getUser();\r\n      }\r\n      this.cdr.detectChanges();\r\n    });\r\n\r\n    this.router.events.subscribe(() => {\r\n      this.showBackIcon = this.router.url !== '/search';\r\n\r\n      if(this.router.url === '/create'){\r\n        this.appName = 'Criar Grupo';\r\n      } else if(this.router.url === '/my-study-group'){\r\n        this.appName = 'Meus Grupos';\r\n      } else if(this.router.url.startsWith('/edit')){\r\n        this.appName = 'Editar';\r\n      } else if(this.router.url.startsWith('/detail')){\r\n        this.appName = 'Detalhes';\r\n      } else {\r\n        this.appName = 'Bora Estudar UFF';\r\n      }\r\n    });\r\n  }\r\n\r\n  public logout() {\r\n    this.authService.logout().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.router.navigateByUrl('/login');\r\n        this.close();\r\n\r\n        this.snackBar.open(\r\n          'Desconectado com sucesso!',\r\n          'X',\r\n          { duration: 2500 }\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public getUser() {\r\n    this.authService.getUser().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.user = data;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  navigateToSearch(): void {\r\n    this.router.navigate(['/search']);\r\n  }\r\n\r\n  // navigateToSearch(): void {\r\n  //   const previousUrl = this.navigationService.getPreviousUrl();\r\n\r\n  //   if (previousUrl) {\r\n  //       this.router.navigate([previousUrl]);\r\n  //   } else {\r\n  //       this.router.navigate(['/home']);\r\n  //   }\r\n  // }\r\n\r\n  close(){\r\n    if (this.sidenav) {\r\n      this.sidenav.close();\r\n    }\r\n  }\r\n}\r\n", "<body>\r\n  <div class=\"app_container\">\r\n    <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n\r\n      @if (isLoggedIn === true) {\r\n        @if (showBackIcon && this.router.url !== '/associate' ) {\r\n          <button mat-icon-button (click)=\"navigateToSearch()\">\r\n            <mat-icon>arrow_back</mat-icon>\r\n          </button>\r\n        }\r\n\r\n        <span class=\"user-name-toolbar\">Seja bem vindo(a), {{ user?.name || 'Usuário' }}</span>\r\n      }\r\n\r\n      <span class=\"spacer\"></span>\r\n\r\n      <h1 class=\"app_name\">{{appName}}</h1>\r\n\r\n      <span class=\"spacer\"></span>\r\n\r\n      @if (isLoggedIn === true) {\r\n\r\n        <!-- Menu dropdown para logout -->\r\n        <mat-menu #userMenu=\"matMenu\">\r\n          <button mat-menu-item (click)=\"logout()\">\r\n            <mat-icon>logout</mat-icon>\r\n            <span>Sair</span>\r\n          </button>\r\n        </mat-menu>\r\n\r\n        <button mat-icon-button (click)=\"snav.toggle()\">\r\n          <mat-icon>menu</mat-icon>\r\n        </button>\r\n      }\r\n    </mat-toolbar>\r\n\r\n    <mat-sidenav-container class=\"sidenav_container\">\r\n      <mat-sidenav #snav mode=\"over\" class=\"mat_sidenav_content\">\r\n        @if (isLoggedIn === true) {\r\n          <mat-nav-list>\r\n            @if(this.router.url !== '/associate'){\r\n              <a mat-list-item routerLink=\"/search\" (click)=\"close()\">Home</a>\r\n              <a mat-list-item routerLink=\"/create\" (click)=\"close()\">Criar Grupos</a>\r\n              <a mat-list-item routerLink=\"/my-study-group\" (click)=\"close()\">Meus Grupos</a>\r\n            }\r\n            <a mat-list-item (click)=\"logout()\">Sair</a>\r\n          </mat-nav-list>\r\n        } @else {\r\n          <mat-nav-list>\r\n            <a mat-list-item routerLink=\"/login\">Login</a>\r\n            <a mat-list-item routerLink=\"/register\">Register</a>\r\n          </mat-nav-list>\r\n        }\r\n      </mat-sidenav>\r\n\r\n      <mat-sidenav-content>\r\n        <router-outlet><main></main></router-outlet>\r\n      </mat-sidenav-content>\r\n    </mat-sidenav-container>\r\n  </div>\r\n</body>\r\n", "import {Component, OnInit} from '@angular/core';\r\nimport {ActivatedRoute} from \"@angular/router\";\r\nimport {AuthService} from \"../../../core/security/auth/auth.service\";\r\n\r\n@Component({\r\n    selector: 'app-email-confirm',\r\n    templateUrl: './email-confirm.component.html',\r\n    styleUrls: ['./email-confirm.component.scss'],\r\n    standalone: true\r\n})\r\nexport class EmailConfirmComponent implements OnInit {\r\n  message: string = '';\r\n\r\n  constructor(private route: ActivatedRoute, private authService: AuthService) {}\r\n\r\n  ngOnInit() {\r\n\r\n    this.route.queryParams.subscribe(params => {\r\n      const token = params['token'];\r\n\r\n      this.authService.confirmEmail(token).subscribe({\r\n        next: (data) => {\r\n          console.log(data);\r\n          this.message = data;\r\n        },\r\n        error: (err) => {\r\n          console.error(err);\r\n          this.message = 'Confirmation failed. The link might be expired or invalid.';\r\n        },\r\n      });\r\n    });\r\n  }\r\n}\r\n", "<p>email-confirm works!</p>\r\n", "import { Component, OnInit, inject } from '@angular/core';\r\nimport { AuthService } from '../../../core/security/auth/auth.service';\r\nimport { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { SigninBody } from '../../models/signin-body';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { MatButton, MatAnchor } from '@angular/material/button';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { Mat<PERSON><PERSON>, MatCardTitle, MatCardContent } from '@angular/material/card';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styleUrls: ['./login.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        Mat<PERSON>ard,\r\n        MatCardTitle,\r\n        MatCardContent,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormField,\r\n        Mat<PERSON>abe<PERSON>,\r\n        <PERSON>In<PERSON>,\r\n        Mat<PERSON>utton,\r\n        MatAnchor,\r\n        RouterLink,\r\n    ],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  private router = inject(Router);\r\n  private fb = inject(FormBuilder);\r\n  private authService = inject(AuthService);\r\n  protected loginForm!: FormGroup;\r\n  protected errorMessage = '';\r\n  // isLoggedIn$ = this.authService.isLoggedIn();\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {\r\n    // this.authService.isLoggedIn().subscribe((isLogged) => {\r\n    //   this.isLoggedIn$ = isLogged;\r\n    // });\r\n\r\n    // if (this.isLoggedIn) {\r\n    //   this.router.navigateByUrl('/search');\r\n    // }\r\n\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (!this.loginForm.valid) return;\r\n\r\n    const body: SigninBody = {\r\n      email: this.loginForm.controls['email'].value,\r\n      password: this.loginForm.controls['password'].value,\r\n    };\r\n\r\n    this.authService.login(body).subscribe({\r\n      next: (data: any) => {\r\n        console.error('login', data)\r\n        const idUsuario = data.id.toString();\r\n        localStorage.setItem('idUsuario', idUsuario);\r\n        let isDiscordAssociate = data.isDiscordAssociate;\r\n        console.error('isDiscordAssociate', isDiscordAssociate)\r\n        // this.router.navigateByUrl('/search');\r\n\r\n        if(isDiscordAssociate === true){\r\n          this.router.navigateByUrl('/search');\r\n        } else {\r\n          this.router.navigateByUrl('/associate');\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n        this.errorMessage = error.error.message;\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-title> <PERSON><PERSON> - UFF! </mat-card-title>\r\n\r\n    <mat-card-content>\r\n      <form class=\"login-form\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Email</mat-label>\r\n          <input matInput formControlName=\"email\" type=\"email\" required />\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Senha</mat-label>\r\n          <input matInput formControlName=\"password\" type=\"password\" required />\r\n        </mat-form-field>\r\n\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          class=\"form-button\"\r\n          type=\"submit\"\r\n        >\r\n          Login\r\n        </button>\r\n\r\n        <div class=\"linhaou\">\r\n          <span class=\"linha\"> </span>\r\n          <span class=\"ou\">OU</span>\r\n          <span class=\"linha\"> </span>\r\n        </div>\r\n\r\n        <a\r\n          mat-stroked-button\r\n          color=\"link\"\r\n          class=\"form-button\"\r\n          routerLink=\"/register\"\r\n          >Cadastre-se</a\r\n        >\r\n\r\n        <a\r\n          mat-button\r\n          class=\"form-button\"\r\n          color=\"primary\"\r\n          routerLink=\"/password-recovery\"\r\n          >Esqueceu a senha?</a\r\n        >\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n", "import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-password-recovery',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './password-recovery.component.html',\r\n  styleUrl: './password-recovery.component.scss'\r\n})\r\nexport class PasswordRecoveryComponent {\r\n\r\n}\r\n", "<p>password-recovery works!</p>\r\n", "import { Component, OnInit, inject } from '@angular/core';\r\nimport { AuthService } from '../../../core/security/auth/auth.service';\r\nimport { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { SignupBody } from '../../models/signup-body';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { MatButton, MatAnchor } from '@angular/material/button';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { Mat<PERSON>ard, MatCardTitle, MatCardContent } from '@angular/material/card';\r\n\r\n@Component({\r\n    selector: 'app-register',\r\n    templateUrl: './register.component.html',\r\n    styleUrls: ['./register.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        <PERSON><PERSON><PERSON>,\r\n        Mat<PERSON>ardTit<PERSON>,\r\n        Mat<PERSON>ardContent,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormField,\r\n        MatLabel,\r\n        MatInput,\r\n        MatButton,\r\n        MatAnchor,\r\n        RouterLink,\r\n    ],\r\n})\r\nexport class RegisterComponent implements OnInit {\r\n  private router = inject(Router);\r\n  private fb = inject(FormBuilder);\r\n  private authService = inject(AuthService);\r\n  private snackBar = inject(MatSnackBar);\r\n  protected signupForm!: FormGroup;\r\n  protected errorMessage = '';\r\n  isLoggedIn = false;\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.isLoggedIn().subscribe((isLogged) => {\r\n      this.isLoggedIn = isLogged;\r\n    });\r\n\r\n    // if (this.authService.isLoggedIn$.pipe()) {\r\n    //   this.router.navigateByUrl('/search');\r\n    // }\r\n\r\n    this.signupForm = this.fb.group({\r\n      name: ['', [Validators.required]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (!this.signupForm.valid) return;\r\n\r\n    console.log('signup', this.signupForm.value);\r\n\r\n    const body: SignupBody = {\r\n      name: this.signupForm?.value.name,\r\n      email: this.signupForm?.value.email,\r\n      password: this.signupForm?.value.password\r\n    };\r\n\r\n    this.authService.register(body).subscribe({\r\n      next: () => this.onSuccess(),\r\n      error: () => this.onError(),\r\n    });\r\n  }\r\n\r\n  private onSuccess() {\r\n    this.snackBar.open(\r\n      'Registrado com sucesso! Por favor, acesse seu e-mail para confirmar sua conta.',\r\n      'X',\r\n      { duration: 5000 }\r\n    );\r\n    this.signupForm.reset();\r\n    this.router.navigateByUrl('/login');\r\n  }\r\n\r\n  private onError() {\r\n    this.snackBar.open('Erro ao cadastrar.', 'X', { duration: 10000 });\r\n  }\r\n\r\n  private reloadPage(): void {\r\n    window.location.reload();\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-title> <PERSON><PERSON>uda<PERSON> - UFF! </mat-card-title>\r\n\r\n    <mat-card-content>\r\n      <form [formGroup]=\"signupForm\" (ngSubmit)=\"onSubmit()\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Nome</mat-label>\r\n          <input matInput formControlName=\"name\" type=\"text\" required />\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Email</mat-label>\r\n          <input matInput formControlName=\"email\" type=\"email\" required />\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Senha</mat-label>\r\n          <input matInput formControlName=\"password\" type=\"password\" required />\r\n        </mat-form-field>\r\n\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          class=\"form-button\"\r\n          type=\"submit\"\r\n        >\r\n          Cadastrar\r\n        </button>\r\n\r\n        <a\r\n          mat-stroked-button\r\n          color=\"link\"\r\n          class=\"form-button\"\r\n          routerLink=\"/login\"\r\n          >Voltar\r\n        </a>\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n", "import { Injectable } from \"@angular/core\";\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { Observable } from \"rxjs\";\r\n\r\n@Injectable()\r\nexport class HttpRequestInterceptor implements HttpInterceptor {\r\n  intercept(\r\n    req: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    req = req.clone({\r\n      withCredentials: true,\r\n    });\r\n\r\n    return next.handle(req);\r\n  }\r\n}\r\n\r\nexport const httpInterceptorProviders = [\r\n  { provide: HTTP_INTERCEPTORS, useClass: HttpRequestInterceptor, multi: true },\r\n];\r\n", "import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable, inject } from '@angular/core';\r\nimport { Observable, catchError, tap } from 'rxjs';\r\nimport { SigninBody } from 'src/app/auth/models/signin-body';\r\nimport { SignupBody } from 'src/app/auth/models/signup-body';\r\nimport { UserResponseBasicDto } from 'src/app/shared/models/user/user-response-basic-dto';\r\nimport { StorageService } from './storage.service';\r\nimport { environment } from '../../../../environments/environment';\r\n\r\nconst AUTH_API = '';\r\n\r\nconst httpOptions = {\r\n  headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n};\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  private readonly http = inject(HttpClient);\r\n  private readonly storageService = inject(StorageService);\r\n\r\n  login(body: SigninBody): Observable<UserResponseBasicDto> {\r\n    return this.http\r\n      .post<UserResponseBasicDto>(AUTH_API.concat('/signin'), body, httpOptions)\r\n      .pipe(\r\n        tap((user) => {\r\n          this.storageService.saveUser(user);\r\n        }),\r\n        catchError((error) => {\r\n          console.log(`Error on login: ${error.message}`);\r\n          this.storageService.clear();\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  register(body: SignupBody): Observable<UserResponseBasicDto> {\r\n    return this.http\r\n      .post<UserResponseBasicDto>(AUTH_API.concat('/signup'), body, httpOptions)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.log(`Error on login: ${error.message}`);\r\n          this.storageService.clear();\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  confirmEmail(token: string): Observable<any> {\r\n    return this.http.get(\r\n      AUTH_API.concat(`/confirm?token=${token}`),\r\n      httpOptions\r\n    );\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(AUTH_API + '/signout', {}, httpOptions).pipe(\r\n      tap(() => {\r\n        this.storageService.clear();\r\n      }),\r\n      catchError((error) => {\r\n        console.log(`Error on login: ${error.message}`);\r\n        this.storageService.clear();\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  getUser(): Observable<UserResponseBasicDto> {\r\n    return this.storageService.getUser();\r\n  }\r\n\r\n  isLoggedIn(): Observable<boolean> {\r\n    return this.storageService.isLoggedIn();\r\n  }\r\n}\r\n", "import { Injectable, inject } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { UserResponseBasicDto } from 'src/app/shared/models/user/user-response-basic-dto';\r\nimport { of } from 'rxjs';\r\n\r\nconst SIGNED_USER = 'signed-user';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StorageService {\r\n  private signedUser = new BehaviorSubject<UserResponseBasicDto | undefined>(\r\n    undefined\r\n  );\r\n  private hasSignedUser = new BehaviorSubject<boolean>(false);\r\n\r\n  constructor() {\r\n    this.restoreLocalStorage();\r\n  }\r\n\r\n  clear(): void {\r\n    localStorage.clear();\r\n    this.signedUser.next(undefined);\r\n    this.hasSignedUser.next(false);\r\n  }\r\n\r\n  public saveUser(user: UserResponseBasicDto): void {\r\n    localStorage.removeItem(SIGNED_USER);\r\n    localStorage.setItem(SIGNED_USER, JSON.stringify(user));\r\n    this.signedUser.next(user);\r\n    this.hasSignedUser.next(true);\r\n  }\r\n\r\n  public getUser(): Observable<UserResponseBasicDto> {\r\n    const signedUserString = localStorage.getItem(SIGNED_USER);\r\n    if (!signedUserString) {\r\n      throw new Error(`No signed user found in the session storage.`);\r\n    }\r\n\r\n    const signedUser: UserResponseBasicDto = JSON.parse(signedUserString);\r\n    return of(signedUser);\r\n  }\r\n\r\n  public isLoggedIn(): Observable<boolean> {\r\n    return this.hasSignedUser.asObservable();\r\n  }\r\n\r\n  private restoreLocalStorage(): void {\r\n    if (!localStorage.getItem(SIGNED_USER)) {\r\n      return;\r\n    }\r\n\r\n    const signedUserStr = localStorage.getItem(SIGNED_USER)!;\r\n    const signedUserObj = JSON.parse(signedUserStr);\r\n\r\n    this.signedUser.next(signedUserObj);\r\n    this.hasSignedUser.next(true);\r\n  }\r\n}\r\n", "import {\r\n  ActivatedRouteSnapshot,\r\n  CanActivateFn,\r\n  Router,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { inject } from '@angular/core';\r\nimport { AuthService } from '../auth/auth.service';\r\n\r\n/**\r\n * A guard that prevents access to a route if the user is already logged in.\r\n * If the user is logged in, it redirects to the '/search' route.\r\n * If the user is not logged in, it allows access to the route.\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\r\n */\r\nexport const loggedInGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The loggedInGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n  let isLoggedIn = false;\r\n\r\n  authService.isLoggedIn().subscribe((b) => {\r\n    isLoggedIn = b;\r\n  });\r\n\r\n  if (isLoggedIn) {\r\n    router.navigateByUrl('/search');\r\n    return false;\r\n  }\r\n  return true;\r\n};\r\n\r\n/**\r\n * A guard that checks if the user is authenticated before allowing access to a route.\r\n * If the user is not authenticated, it redirects to the login page.\r\n *\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean indicating whether the user is authenticated or not, or a UrlTree to redirect to the login page.\r\n */\r\nexport const authGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The authGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n  let isLoggedIn = false;\r\n\r\n  authService.isLoggedIn().subscribe((b) => {\r\n    isLoggedIn = b;\r\n  });\r\n\r\n  if (!isLoggedIn) {\r\n    router.navigateByUrl('/login');\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n/**\r\n * A guard that prevents access to a route if the user is already logged in.\r\n * If the user is logged in, it redirects to the '/search' route.\r\n * If the user is not logged in, it allows access to the route.\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\r\n */\r\nexport const discordAssociateGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The discordAssociateGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  const signed = localStorage.getItem('signed-user');\r\n\r\n  if (signed) {\r\n    const signedUser = JSON.parse(signed);\r\n    const isDiscordAssociate = signedUser.isDiscordAssociate;\r\n    if(!isDiscordAssociate){\r\n      router.navigateByUrl('/associate');\r\n    }\r\n  } else {\r\n    console.error('Usuário não encontrado no localStorage');\r\n  }\r\n  // router.navigateByUrl('/search');\r\n  return true;\r\n};\r\n", "import { Component } from '@angular/core';\r\nimport { Mat<PERSON>av<PERSON>ist, MatListItem } from '@angular/material/list';\r\nimport { MatSidenavContainer, MatSidenav } from '@angular/material/sidenav';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatIconButton } from '@angular/material/button';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\n\r\n@Component({\r\n    selector: 'app-header',\r\n    // standalone: true,\r\n    templateUrl: './header.component.html',\r\n    styleUrl: './header.component.scss',\r\n    standalone: true,\r\n    imports: [\r\n        MatToolbar,\r\n        MatIconButton,\r\n        MatIcon,\r\n        MatSidenavContainer,\r\n        MatSidenav,\r\n        MatNavList,\r\n        MatListItem,\r\n    ],\r\n})\r\nexport class HeaderComponent {\r\n\r\n  fillerNav = Array.from({ length: 50 }, (_, i) => `Nav Item ${i + 1}`);\r\n\r\n  // fillerContent = Array.from(\r\n  //   { length: 3 },\r\n  //   () =>\r\n  //     `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut\r\n  //      labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco\r\n  //      laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in\r\n  //      voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat\r\n  //      cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.`\r\n  // );\r\n\r\n}\r\n", "<div class=\"header_container\">\r\n  <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n    <button mat-icon-button (click)=\"snav.toggle()\">\r\n      <mat-icon>menu</mat-icon>\r\n    </button>\r\n    <span class=\"spacer\"></span>\r\n    <h1 class=\"app_name\">Bora Estudar UFF</h1>\r\n  </mat-toolbar>\r\n\r\n  <mat-sidenav-container class=\"sidenav_container\">\r\n    <mat-sidenav\r\n      #snav\r\n      mode=\"side\"\r\n      class=\"mat_sidenav_content\"\r\n    >\r\n      <mat-nav-list>\r\n        @for (nav of fillerNav; track nav) {\r\n        <a mat-list-item routerLink=\".\">{{ nav }}</a>\r\n        }\r\n      </mat-nav-list>\r\n    </mat-sidenav>\r\n  </mat-sidenav-container>\r\n</div>\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HeaderComponent } from './components/header/header.component';\r\nimport { AngularMaterialModule } from '../angular-material.module';\r\n\r\n@NgModule({\r\n    imports: [CommonModule, AngularMaterialModule, HeaderComponent],\r\n    exports: [HeaderComponent],\r\n})\r\nexport class SharedModule {\r\n}\r\n", "import { ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON>ement<PERSON>ef, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { NgFor } from '@angular/common';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatButton } from '@angular/material/button';\r\nimport { MatCheckbox, MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { StudyGroupSearchItemComponent } from '../study-group-search-item/study-group-search-item.component';\r\nimport { StudyGroupSearchListComponent } from '../study-group-search-list/study-group-search-list.component';\r\n\r\n@Component({\r\n  selector: 'app-my-study-group',\r\n  standalone: true,\r\n  imports: [\r\n    MatFormField,\r\n    MatLabel,\r\n    MatInput,\r\n    MatButton,\r\n    MatIcon,\r\n    MatMenuModule,\r\n    MatCheckboxModule,\r\n    StudyGroupSearchListComponent,\r\n    NgFor,\r\n    StudyGroupSearchItemComponent,\r\n    MatAutocompleteModule\r\n  ],\r\n  templateUrl: './my-study-group.component.html',\r\n  styleUrl: './my-study-group.component.scss'\r\n})\r\nexport class MyStudyGroupComponent implements OnInit {\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  selectedDays: Set<string> = new Set();\r\n  selectedHour: string = '';\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('time') time!: ElementRef<HTMLInputElement>;\r\n  @ViewChildren(MatCheckbox) checkboxes!: QueryList<MatCheckbox>;\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    const id = Number(idUsuario);\r\n\r\n    this.service.getStudyGroupsFind(id).subscribe((dados) => {\r\n      console.log('Dados carregados:', dados);\r\n      this.service.myStudyGroups = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.myStudyGroups.filter(option =>\r\n      option.title.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  applyFilters(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n\r\n    // Dividir o valor do filtro em partes, se necessário\r\n    const [codeFilter, titleFilter] = filterValue.split(' - ').map(part => part.trim());\r\n\r\n    const filter = this.service.myStudyGroups?.filter(option =>\r\n      this.filterByDayOfWeek(option) &&\r\n      this.filterByHour(option) &&\r\n      (option.code.toLowerCase().includes(codeFilter) ||\r\n       option.title.toLowerCase().includes(titleFilter))\r\n    ) || [];\r\n\r\n    this.options = [...filter];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.input.nativeElement.value = '';\r\n    this.time.nativeElement.value = '';\r\n    this.checkboxes.forEach(checkbox => checkbox.checked = false);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  filterByDayOfWeek(option: any): boolean {\r\n    if (!option.daysOfWeek || this.selectedDays.size === 0) {\r\n      return true; // Sem filtro de dia da semana ou dados não definidos\r\n    }\r\n    return option.daysOfWeek.some((day: string) => this.selectedDays.has(day.toLowerCase()));\r\n  }\r\n\r\n  filterByHour(option: any): boolean {\r\n    if (!this.selectedHour) {\r\n      return true; // Sem filtro de horário\r\n    }\r\n    return option.hour >= this.selectedHour;\r\n  }\r\n\r\n  days(day: string): void {\r\n    if (this.selectedDays.has(day)) {\r\n      this.selectedDays.delete(day);\r\n    } else {\r\n      this.selectedDays.add(day);\r\n    }\r\n  }\r\n\r\n  onHourChange(event: any): void {\r\n    this.selectedHour = event.target.value;\r\n  }\r\n}\r\n", "<div class=\"search_container\">\r\n  <div class=\"search_row\">\r\n    <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n      <mat-label>Disciplina</mat-label>\r\n      <input #input matInput class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n      <mat-autocomplete requireSelection #auto=\"matAutocomplete\">\r\n        <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.title\">\r\n          ({{ option.code }}) {{ option.title }}\r\n        </mat-option>\r\n      </mat-autocomplete>\r\n    </mat-form-field>\r\n\r\n    <button\r\n      mat-raised-button\r\n      color=\"primary\"\r\n      class=\"search_row_search_item\"\r\n      (click)=\"applyFilters()\">\r\n      <mat-icon class=\"search_row_icon\">search</mat-icon>\r\n    </button>\r\n\r\n    <button\r\n    mat-raised-button\r\n    color=\"primary\"\r\n    class=\"search_row_search_item\"\r\n    (click)=\"clearFilters()\">Limpar Filtro\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"search_row_2\">\r\n    <button mat-button [matMenuTriggerFor]=\"menu\" class=\"pesquisarButton\"><PERSON><PERSON></button>\r\n    <mat-menu #menu=\"matMenu\" style=\"max-width: auto !important;\">\r\n      <div class=\"position\">\r\n        <section style=\"display: flex;\">\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('dom')\">DOM</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('seg')\">SEG</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('ter')\">TER</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qua')\">QUA</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qui')\">QUI</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sex')\">SEX</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sab')\">SAB</mat-checkbox>\r\n        </section>\r\n      </div>\r\n    </mat-menu>\r\n\r\n    <button mat-button [matMenuTriggerFor]=\"menuHora\" class=\"pesquisarButton\">Hora de Início</button>\r\n    <mat-menu #menuHora=\"matMenu\" class=\"position\">\r\n      <div class=\"position\">\r\n        <mat-form-field appearance=\"outline\" class=\"input6\" (click)=\"$event.stopPropagation()\">\r\n          <mat-label>A partir de:</mat-label>\r\n          <input #time matInput type=\"time\" (change)=\"onHourChange($event)\"/>\r\n        </mat-form-field>\r\n      </div>\r\n    </mat-menu>\r\n  </div>\r\n\r\n  <div class=\"container\">\r\n    <div *ngFor=\"let groups of options\">\r\n      <app-study-group-search-item [studyGroup]=\"groups\"></app-study-group-search-item>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { Injectable } from '@angular/core';\r\nimport { Router, NavigationStart } from '@angular/router';\r\nimport { filter } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NavigationServiceService {\r\n\r\n\r\n  private previousUrls: string[] = [];\r\n  private currentUrl: string | null = null;\r\n\r\n  constructor(private router: Router) {\r\n    this.router.events\r\n      .pipe(\r\n        filter(event => event instanceof NavigationStart)\r\n      )\r\n      .subscribe((event) => {\r\n        const navigationStartEvent = event as NavigationStart;\r\n        if (this.currentUrl) {\r\n          this.previousUrls.push(this.currentUrl);\r\n        }\r\n        this.currentUrl = navigationStartEvent.url;\r\n        console.log('Current URL:', this.currentUrl); // Debugging log\r\n        console.log('Previous URLs:', this.previousUrls); // Debugging log\r\n      });\r\n  }\r\n\r\n  public getPreviousUrl(): string | null {\r\n    return this.previousUrls.length > 0 ? this.previousUrls[this.previousUrls.length - 1] : null;\r\n  }\r\n\r\n  public navigateToPrevious(): void {\r\n    const previousUrl = this.getPreviousUrl();\r\n    if (previousUrl) {\r\n      this.router.navigateByUrl(previousUrl).catch(error => console.error('Navigation error:', error));\r\n      // Remove the last entry as we navigated to it\r\n      this.previousUrls.pop();\r\n    } else {\r\n      this.router.navigate(['/search']).catch(error => console.error('Navigation error:', error));\r\n    }\r\n  }\r\n}\r\n", "import { CommonModule, NgFor } from '@angular/common';\r\nimport { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatIconButton, MatButton } from '@angular/material/button';\r\nimport { MatChipListbox, MatChipOption } from '@angular/material/chips';\r\nimport { MatOption } from '@angular/material/core';\r\nimport { MatDialogRef } from '@angular/material/dialog';\r\nimport { MatForm<PERSON>ield, MatLabel } from '@angular/material/form-field';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatSelect } from '@angular/material/select';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Router } from '@angular/router';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Subscription } from 'rxjs';\r\n\r\n\r\n@Component({\r\n  selector: 'app-study-create-group',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatToolbar,\r\n    MatIconButton,\r\n    MatIcon,\r\n    MatFormField,\r\n    MatLabel,\r\n    MatSelect,\r\n    FormsModule,\r\n    NgFor,\r\n    MatOption,\r\n    MatChipListbox,\r\n    MatChipOption,\r\n    MatInput,\r\n    MatButton,\r\n    MatAutocompleteModule,\r\n    ToastModule,\r\n    ProgressSpinnerModule,\r\n  ],\r\n  templateUrl: './study-create-group.component.html',\r\n  styleUrl: './study-create-group.component.scss',\r\n  providers: [MessageService],\r\n})\r\n\r\nexport class StudyCreateGroupComponent implements OnInit {\r\n  formulario!: UntypedFormGroup;\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n\r\n  protected daysOfWeek: { id: number; name: string }[] = [\r\n    { id: 1, name: 'Dom' },\r\n    { id: 2, name: 'Seg' },\r\n    { id: 3, name: 'Ter' },\r\n    { id: 4, name: 'Qua' },\r\n    { id: 5, name: 'Qui' },\r\n    { id: 6, name: 'Sex' },\r\n    { id: 7, name: 'Sáb' },\r\n  ];\r\n\r\n  protected modalities: { value: string; viewValue: string }[] = [\r\n    { value: 'presencial', viewValue: 'Presencial' },\r\n    { value: 'online', viewValue: 'Online' },\r\n    { value: 'hibrido', viewValue: 'Híbrido' },\r\n  ];\r\n\r\n  protected selectedModality: String | null = null;\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private messageService: MessageService,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private builder: UntypedFormBuilder\r\n  ) {\r\n    this.formulario = this.builder.group({\r\n      title: [''],\r\n      description: [''],\r\n      campoOculto: [''],\r\n      subject: [''],\r\n      maxStudents: [''],\r\n      meetingTime: [''],\r\n      modality: ['REMOTE'],\r\n      weekdays: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.service.getSubjects().subscribe((dados) => {\r\n      console.error('Dados carregados:', dados);\r\n      this.service.subjects = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    });\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.subjects.filter(\r\n      (option) =>\r\n        option.id.toString().includes(filterValue) ||\r\n        option.name.toLowerCase().includes(filterValue) ||\r\n        option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  onOptionSelected(option: any): void {\r\n    this.formulario.get('subject')?.patchValue(option);\r\n    this.formulario\r\n      .get('campoOculto')\r\n      ?.patchValue(option.code + ' - ' + option.name);\r\n  }\r\n\r\n  private subscription: Subscription | null = null;\r\n\r\n  createGroups() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    const id = Number(idUsuario);\r\n    const title =\r\n      this.formulario?.value.subject.code +\r\n      ' ' +\r\n      this.formulario?.value.subject.name;\r\n\r\n    const studyGroupData = {\r\n      title: title,\r\n      description: this.formulario?.value.description,\r\n      ownerId: id,\r\n      subject: this.formulario?.value.subject,\r\n      weekdays: this.formulario?.value.weekdays,\r\n      meetingTime: this.formulario?.value.meetingTime,\r\n      maxStudents: this.formulario?.value.maxStudents,\r\n      modality: this.formulario?.value.modality,\r\n    };\r\n\r\n    this.subscription = this.service\r\n      .createStudyGroup(studyGroupData)\r\n      .subscribe({\r\n        next: (response) => {\r\n          console.error('Grupo de estudo criado com sucesso:', response);\r\n\r\n          const mappedStudyGroup = this.service.mappingStudyGroup(response);\r\n          this.service.setStudyGroup(mappedStudyGroup);\r\n\r\n          this.snackBar.open('Grupo de estudo criado com sucesso!', 'X', {\r\n            duration: 2500,\r\n          });\r\n          this.router.navigate([`/detail/${response.id}`]);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao criar grupo de estudo:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription?.unsubscribe(); // Cancela a assinatura ao destruir o componente\r\n  }\r\n}\r\n", "<div class=\"filter-container\">\r\n  <form [formGroup]=\"formulario\" >\r\n    <div class=\"row\">\r\n      <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n        <mat-label>Disciplina</mat-label>\r\n        <input #input matInput formControlName=\"campoOculto\" class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n        <mat-autocomplete requireSelection #auto=\"matAutocomplete\" (optionSelected)=\"onOptionSelected($event.option.value)\">\r\n          <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option\">\r\n          <!-- <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.name\"> -->\r\n            ({{ option.code }}) {{ option.name }}\r\n          </mat-option>\r\n        </mat-autocomplete>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <mat-form-field class=\"input100\">\r\n      <mat-label>Descrição</mat-label>\r\n      <textarea matInput formControlName=\"description\"></textarea>\r\n    </mat-form-field>\r\n\r\n    <div class=\"row\">\r\n      <mat-form-field>\r\n        <mat-label>Número de Alunos</mat-label>\r\n        <mat-select formControlName=\"maxStudents\">\r\n          <mat-option [value]=\"2\">2</mat-option>\r\n          <mat-option [value]=\"3\">3</mat-option>\r\n          <mat-option [value]=\"4\">4</mat-option>\r\n          <mat-option [value]=\"5\">5</mat-option>\r\n          <mat-option [value]=\"6\">6</mat-option>\r\n        </mat-select>\r\n      </mat-form-field>\r\n\r\n      <mat-form-field class=\"margin input30\">\r\n        <mat-label>Hora de Início</mat-label>\r\n        <input matInput type=\"time\" formControlName=\"meetingTime\"/>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <h2>Dias da Semana</h2>\r\n    <mat-chip-listbox formControlName=\"weekdays\" multiple>\r\n      <mat-chip-option *ngFor=\"let day of daysOfWeek\" [value]=\"day\">\r\n        {{ day.name }}\r\n      </mat-chip-option>\r\n    </mat-chip-listbox>\r\n\r\n    <div class=\"allCenter\">\r\n      <p-toast />\r\n\r\n      <button mat-raised-button color=\"primary\" class=\"input100\" (click)=\"createGroups()\">Criar Grupo</button>\r\n    </div>\r\n  </form>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/button';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-study-group-associate',\r\n  standalone: true,\r\n  imports: [\r\n    Mat<PERSON>utt<PERSON>\r\n  ],\r\n  templateUrl: './study-group-associate.component.html',\r\n  styleUrl: './study-group-associate.component.scss'\r\n})\r\nexport class StudyGroupAssociateComponent implements OnInit {\r\n  private encodedApiUrl = environment.encodedApiUrl;\r\n\r\n  constructor(\r\n    public service: StudyGroupService){}\r\n\r\n  ngOnInit(): void {\r\n\r\n  }\r\n\r\n  associate() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    // const url = `https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}`;\r\n    const url = `https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=${this.encodedApiUrl}%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}`;\r\n    https: window.open(url, '_blank');\r\n  }\r\n\r\n}\r\n", "<div class=\"m-padding\">\r\n  <div class=\"allCenter\">\r\n    <p class=\"titulo\"><b>Associe sua conta no Discord para utilizar o Bora Estudar - UFF!</b></p>\r\n  </div>\r\n\r\n  <div class=\"justify\">\r\n    <p>Para utilizar todas as funções do Bora Estudar - UFF, é necessário que você associe uma conta no Discord. Clique no botão abaixo para fazer a associação e comece a utilizar.</p>\r\n  </div>\r\n\r\n  <button mat-raised-button color=\"primary\" class=\"full_width m-top\" (click)=\"associate()\">Associe sua Conta!</button>\r\n</div>\r\n", "import { Component, Inject, Input, OnInit, inject } from '@angular/core';\r\nimport { StudyGroup } from '../study-group';\r\nimport { StudyGroupMockService } from '../study-group-mock.service';\r\nimport { ActivatedRoute, ParamMap, Router, RouterLink } from '@angular/router';\r\nimport { Observable, catchError, of } from 'rxjs';\r\nimport { MatButton, MatIconButton } from '@angular/material/button';\r\nimport { NgFor, AsyncPipe, TitleCasePipe, CommonModule } from '@angular/common';\r\nimport { MatChipSet, MatChip } from '@angular/material/chips';\r\nimport { MatCard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, MatListItem } from '@angular/material/list';\r\nimport { <PERSON><PERSON>idenavContainer, MatSidenav } from '@angular/material/sidenav';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-study-group-detail',\r\n  templateUrl: './study-group-detail.component.html',\r\n  styleUrls: ['./study-group-detail.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    MatToolbar,\r\n    MatCard,\r\n    MatCardHeader,\r\n    MatCardTitle,\r\n    MatCardSubtitle,\r\n    MatCardContent,\r\n    MatChipSet,\r\n    NgFor,\r\n    MatChip,\r\n    MatCardActions,\r\n    MatButton,\r\n    RouterLink,\r\n    AsyncPipe,\r\n    TitleCasePipe,\r\n    MatIcon,\r\n    MatIconButton,\r\n    MatSidenavContainer,\r\n    MatSidenav,\r\n    MatNavList,\r\n    MatListItem,\r\n    ToastModule,\r\n    ProgressSpinnerModule,\r\n  ],\r\n  providers: [MessageService],\r\n})\r\nexport class StudyGroupDetailComponent implements OnInit {\r\n  studyGroup: any;\r\n  diasSemana = ['dom', 'seg', 'ter', 'qua', 'qui', 'sex', 'sáb'];\r\n  userInGroup!: boolean;\r\n  isOwnerId!: boolean;\r\n  loading: boolean = false;\r\n  discordInviteUrl: string | null = null;\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private router: Router,\r\n    public service: StudyGroupService,\r\n    private route: ActivatedRoute,\r\n    private messageService: MessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.studyGroup = this.service.getStudyGroup();\r\n    this.callGroup();\r\n  }\r\n\r\n  openDiscord() {\r\n    if (this.discordInviteUrl) {\r\n      window.open(this.discordInviteUrl, '_blank', 'noopener,noreferrer');\r\n    } else {\r\n      this.snackBar.open('Link do Discord não disponível', 'Fechar', {\r\n        duration: 3000,\r\n        panelClass: ['error-snackbar'],\r\n      });\r\n    }\r\n  }\r\n\r\n  joinGroup() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n\r\n    if (idUsuario !== null) {\r\n      const id = Number(idUsuario);\r\n      this.service.joinGroupService(this.studyGroup.id, id).subscribe({\r\n        next: (response) => {\r\n          this.snackBar.open('Entrou no grupo com sucesso!', 'X', {\r\n            duration: 2500,\r\n          });\r\n\r\n          this.loading = false;\r\n\r\n          this.callGroup();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao entrar no grupo:', error);\r\n        },\r\n      });\r\n    } else {\r\n      console.error('ID do usuário não encontrado no localStorage');\r\n    }\r\n  }\r\n\r\n  leaveGroup() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    this.loading = true;\r\n\r\n    if (idUsuario !== null) {\r\n      const id = Number(idUsuario);\r\n      this.service.leaveGroupService(this.studyGroup.id, id).subscribe({\r\n        next: (resposta) => {\r\n          this.snackBar.open('Saiu do grupo com sucesso!', 'X', {\r\n            duration: 5000,\r\n          });\r\n          this.loading = false;\r\n\r\n          this.callGroup();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao sair do grupo:', error);\r\n          this.loading = false;\r\n          this.snackBar.open('Erro ao sair do grupo!', '', { duration: 5000 });\r\n        },\r\n      });\r\n    } else {\r\n      console.error('ID do usuário não encontrado no localStorage');\r\n    }\r\n  }\r\n\r\n  editGroup() {\r\n    if (this.studyGroup) {\r\n      this.router.navigate(['/edit'], {\r\n        queryParams: { id: this.studyGroup.id },\r\n      });\r\n    }\r\n  }\r\n\r\n  callGroup() {\r\n    const idParam = this.route.snapshot.paramMap.get('groupId');\r\n    const idDetail = idParam ? Number(idParam) : null;\r\n\r\n    if (idDetail !== null) {\r\n      this.service.getStudyGroupId(idDetail).subscribe({\r\n        next: (response) => {\r\n          console.error('grupo de estudo - response:', response);\r\n          const mappedStudyGroup = this.service.mappingStudyGroup(response);\r\n          this.studyGroup = mappedStudyGroup;\r\n          this.discordInviteUrl = this.studyGroup.discordInviteUrl; // Assumindo que o invite está nesta propriedade\r\n\r\n          console.error('grupo de estudo - detalhe:', mappedStudyGroup);\r\n\r\n          const idUsuario = localStorage.getItem('idUsuario');\r\n          const id = Number(idUsuario);\r\n\r\n          if (this.studyGroup && Array.isArray(this.studyGroup.students)) {\r\n            const isStudentInGroup = this.studyGroup.students.some(\r\n              (student: any) => student.id === id\r\n            );\r\n            this.userInGroup = isStudentInGroup;\r\n\r\n            const isOwner = this.studyGroup.ownerId === id;\r\n            this.isOwnerId = isOwner;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.router.navigate([`/search`]);\r\n        },\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<!-- <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n  <button mat-icon-button (click)=\"close()\">\r\n    <mat-icon>arrow_back</mat-icon>\r\n  </button>\r\n\r\n  <span class=\"spacer\"></span>\r\n\r\n  <span>Detalhes</span>\r\n\r\n  <span class=\"spacer\"></span>\r\n</mat-toolbar> -->\r\n\r\n<div class=\"allGrid m-top\">\r\n  <mat-card-title class=\"titulo\">{{ studyGroup?.code + ' - ' + studyGroup?.title }}</mat-card-title>\r\n  <mat-card-subtitle class=\"subTitulo\">{{ studyGroup?.shortDescription }}</mat-card-subtitle>\r\n\r\n  <div class=\"allBetween m-top\">\r\n    <p><strong>Alunos:</strong> {{ studyGroup?.participants }}</p>\r\n    <p><strong><PERSON>ra de Início:</strong> {{ studyGroup?.hour }}</p>\r\n  </div>\r\n\r\n  <div>\r\n    <p><strong>Dias da Semana</strong></p>\r\n    <mat-chip-set>\r\n      <mat-chip *ngFor=\"let day of diasSemana\" [ngClass]=\"{'selected': studyGroup?.daysOfWeek.includes(day)}\">\r\n        <p [ngClass]=\"{'selectedText': studyGroup?.daysOfWeek.includes(day)}\" style=\"margin-top: 12px;\">{{ day | titlecase }}</p>\r\n      </mat-chip>\r\n    </mat-chip-set>\r\n  </div>\r\n\r\n  @if(loading === true){\r\n    <p-progressSpinner class=\"spinerCenter\" ariaLabel=\"loading\" />\r\n  } @else {\r\n    @if(this.userInGroup === true){\r\n      <button mat-raised-button\r\n              color=\"primary\"\r\n              class=\"full_width mtop\"\r\n              (click)=\"openDiscord()\"\r\n              [disabled]=\"!discordInviteUrl\">\r\n        <mat-icon fontIcon=\"discord\"></mat-icon>\r\n        Entrar no Discord\r\n      </button>\r\n\r\n      @if(this.isOwnerId === true){\r\n        <button mat-raised-button color=\"primary\" class=\"full_width mtop\" (click)=\"editGroup()\">Editar</button>\r\n      }\r\n      <button mat-raised-button class=\"full_width mtop\" (click)=\"leaveGroup()\">Sair</button>\r\n    } @else {\r\n      <button mat-raised-button color=\"primary\" class=\"full_width mtop\" (click)=\"joinGroup()\">Entrar</button>\r\n    }\r\n  }\r\n</div>\r\n\r\n<p-toast />\r\n\r\n<!-- <div class=\"container\">\r\n  @if( studyGroup$ | async ; as studyGroup) { @if(!!studyGroup){\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title>{{ studyGroup.title }}</mat-card-title>\r\n      <mat-card-subtitle>{{ studyGroup.shortDescription }}</mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p><strong>Monitor:</strong> {{ studyGroup.monitor }}</p>\r\n      <p><strong>Alunos:</strong> {{ studyGroup.participants }}</p>\r\n      <p><strong>Modalidade:</strong> {{ studyGroup.modality | titlecase }}</p>\r\n      <p><strong>Horario:</strong> {{ studyGroup.hour }}</p>\r\n      <mat-chip-set>\r\n        <mat-chip *ngFor=\"let day of studyGroup.daysOfWeek\">{{\r\n          day | titlecase\r\n        }}</mat-chip>\r\n      </mat-chip-set>\r\n    </mat-card-content>\r\n    <mat-card-actions>\r\n      <button\r\n        mat-raised-button\r\n        color=\"secondary\"\r\n        class=\"full_width\"\r\n        [routerLink]=\"['/study-group', studyGroup.id]\"\r\n      >\r\n        Detalhes\r\n      </button>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        class=\"full_width\"\r\n        [routerLink]=\"['/study-group', studyGroup.id]\"\r\n      >\r\n        Detalhes\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n  } @else {\r\n  <p>Error loading data</p>\r\n  } } @else {\r\n  <p>Loading...</p>\r\n  }\r\n</div> -->\r\n", "import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON>ement<PERSON><PERSON>, On<PERSON>nit, Query<PERSON>ist, ViewChild, ViewChildren, inject } from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { StudyGroupFilterDialogComponent } from '../study-group-filter-dialog/study-group-filter-dialog.component';\r\nimport { StudyGroupSearchListComponent } from '../study-group-search-list/study-group-search-list.component';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatButton } from '@angular/material/button';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatForm<PERSON>ield, MatLabel } from '@angular/material/form-field';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatCheckbox, MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { StudyGroupSearchItemComponent } from '../study-group-search-item/study-group-search-item.component';\r\nimport { <PERSON><PERSON><PERSON> } from '@angular/common';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { Router } from '@angular/router';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@Component({\r\n    selector: 'app-study-group-search-bar',\r\n    templateUrl: './study-group-search-bar.component.html',\r\n    styleUrl: './study-group-search-bar.component.scss',\r\n    standalone: true,\r\n    imports: [\r\n        MatFormField,\r\n        MatLabel,\r\n        MatInput,\r\n        MatButton,\r\n        MatIcon,\r\n        MatMenuModule,\r\n        MatCheckboxModule,\r\n        StudyGroupSearchListComponent,\r\n        NgFor,\r\n        StudyGroupSearchItemComponent,\r\n        MatAutocompleteModule,\r\n        ToastModule\r\n    ],\r\n    providers: [MessageService]\r\n})\r\nexport class StudyGroupSearchBarComponent implements OnInit {\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  selectedDays: Set<string> = new Set();\r\n  selectedHour: string = '';\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('time') time!: ElementRef<HTMLInputElement>;\r\n  @ViewChildren(MatCheckbox) checkboxes!: QueryList<MatCheckbox>;\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.service.getStudyGroups().subscribe((dados) => {\r\n      this.service.studyGroups = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.studyGroups.filter(option =>\r\n      option.title.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  applyFilters(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n\r\n    // Dividir o valor do filtro em partes, se necessário\r\n    const [codeFilter, titleFilter] = filterValue.split(' - ').map(part => part.trim());\r\n\r\n    const filter = this.service.studyGroups?.filter(option =>\r\n      this.filterByDayOfWeek(option) &&\r\n      this.filterByHour(option) &&\r\n      (option.code.toLowerCase().includes(codeFilter) ||\r\n       option.title.toLowerCase().includes(titleFilter))\r\n    ) || [];\r\n\r\n    this.options = [...filter];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.input.nativeElement.value = '';\r\n    this.time.nativeElement.value = '';\r\n    this.checkboxes.forEach(checkbox => checkbox.checked = false);\r\n    this.cdr.detectChanges();\r\n\r\n    this.service.getStudyGroups().subscribe((dados) => {\r\n      this.service.studyGroups = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n  }\r\n\r\n  filterByDayOfWeek(option: any): boolean {\r\n    if (!option.daysOfWeek || this.selectedDays.size === 0) {\r\n      return true; // Sem filtro de dia da semana ou dados não definidos\r\n    }\r\n    return option.daysOfWeek.some((day: string) => this.selectedDays.has(day.toLowerCase()));\r\n  }\r\n\r\n  filterByHour(option: any): boolean {\r\n    if (!this.selectedHour) {\r\n      return true; // Sem filtro de horário\r\n    }\r\n    return option.hour >= this.selectedHour;\r\n  }\r\n\r\n  days(day: string): void {\r\n    if (this.selectedDays.has(day)) {\r\n      this.selectedDays.delete(day);\r\n    } else {\r\n      this.selectedDays.add(day);\r\n    }\r\n  }\r\n\r\n  onHourChange(event: any): void {\r\n    this.selectedHour = event.target.value;\r\n  }\r\n\r\n  navigateCreate(): void {\r\n    this.router.navigate(['/create']);\r\n  }\r\n}\r\n", "<div class=\"search_container\">\r\n  <div class=\"search_row\">\r\n    <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n      <mat-label>Disciplina</mat-label>\r\n      <input #input matInput class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n      <mat-autocomplete requireSelection #auto=\"matAutocomplete\">\r\n        <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.title\">\r\n          ({{ option.code }}) {{ option.title }}\r\n        </mat-option>\r\n      </mat-autocomplete>\r\n    </mat-form-field>\r\n\r\n    <button\r\n      mat-raised-button\r\n      color=\"primary\"\r\n      class=\"search_row_search_item\"\r\n      (click)=\"applyFilters()\">\r\n      <mat-icon class=\"search_row_icon\">search</mat-icon>\r\n    </button>\r\n\r\n    <button\r\n    mat-raised-button\r\n    color=\"primary\"\r\n    class=\"search_row_search_item\"\r\n    (click)=\"clearFilters()\">Limpar Filtro\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"search_row_2\">\r\n    <button mat-button [matMenuTriggerFor]=\"menu\" class=\"pesquisarButton\"><PERSON><PERSON> da <PERSON></button>\r\n    <mat-menu #menu=\"matMenu\" style=\"max-width: auto !important;\">\r\n      <div class=\"position\">\r\n        <section style=\"display: flex;\" #semanas>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('dom')\">DOM</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('seg')\">SEG</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('ter')\">TER</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qua')\">QUA</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qui')\">QUI</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sex')\">SEX</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sab')\">SAB</mat-checkbox>\r\n        </section>\r\n      </div>\r\n    </mat-menu>\r\n\r\n    <button mat-button [matMenuTriggerFor]=\"menuHora\" class=\"pesquisarButton\">Hora de Início</button>\r\n    <mat-menu #menuHora=\"matMenu\" class=\"position\">\r\n      <div class=\"position\">\r\n        <mat-form-field appearance=\"outline\" class=\"input6\" (click)=\"$event.stopPropagation()\">\r\n          <mat-label>A partir de:</mat-label>\r\n          <input #time matInput type=\"time\" (change)=\"onHourChange($event)\"/>\r\n        </mat-form-field>\r\n      </div>\r\n    </mat-menu>\r\n  </div>\r\n\r\n  <div class=\"example-button-container\">\r\n    <button mat-button class=\"fabButton\" (click)=\"navigateCreate()\">\r\n      <mat-icon>add</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"container\">\r\n    <div *ngFor=\"let groups of options\">\r\n      <app-study-group-search-item\r\n      [studyGroup]=\"groups\">\r\n      </app-study-group-search-item>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<p-toast />\r\n", "import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { StudyGroup } from '../study-group';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { <PERSON><PERSON><PERSON>on } from '@angular/material/button';\r\nimport { Ng<PERSON><PERSON>, TitleCasePipe } from '@angular/common';\r\nimport { MatChipSet, MatChip } from '@angular/material/chips';\r\nimport { <PERSON><PERSON>ard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\r\nimport { StudyGroupService } from '../study-group.service';\r\n\r\n@Component({\r\n    selector: 'app-study-group-search-item',\r\n    templateUrl: './study-group-search-item.component.html',\r\n    styleUrls: ['./study-group-search-item.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        <PERSON><PERSON><PERSON>,\r\n        Mat<PERSON>ardHeader,\r\n        Mat<PERSON>ardTitle,\r\n        <PERSON><PERSON>ardSubtitle,\r\n        <PERSON><PERSON><PERSON><PERSON><PERSON>nt,\r\n        MatChipSet,\r\n        <PERSON><PERSON><PERSON>,\r\n        Mat<PERSON><PERSON>,\r\n        <PERSON><PERSON>ardActions,\r\n        MatButton,\r\n        RouterLink,\r\n        TitleCasePipe,\r\n        MatTooltipModule,\r\n    ],\r\n})\r\nexport class StudyGroupSearchItemComponent {\r\n  @Input() studyGroup!: StudyGroup;\r\n\r\n  constructor(\r\n    public service: StudyGroupService,\r\n    private router: Router) {}\r\n\r\n  openDetalheDialog(studyGroup: any): void {\r\n    this.service.setStudyGroup(studyGroup);\r\n    this.router.navigate([`/detail/${studyGroup.id}`]);\r\n\r\n    // let dialogRef = this.dialog.open(StudyGroupDetailComponent, {\r\n    //   maxWidth: '100vw',\r\n    //   maxHeight: '100vh',\r\n    //   height: '100%',\r\n    //   width: '100%',\r\n    //   data: { id: id }\r\n    // });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title class=\"study_group_item_title limited-title\" matTooltip=\"{{ studyGroup.title }}\">{{ studyGroup.title }}</mat-card-title>\r\n      <mat-card-subtitle class=\"limited-subtitle\" matTooltip=\"{{ studyGroup.shortDescription }}\">{{ studyGroup.shortDescription }}</mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <!-- <p>\r\n        <strong>Monitor:</strong>\r\n        {{ studyGroup.monitor }}\r\n      </p> -->\r\n      <p>\r\n        <strong>Alunos:</strong>\r\n        {{ studyGroup.participants }}\r\n      </p>\r\n      <p>\r\n        <strong>Modalidade:</strong>\r\n        {{ studyGroup.modality | titlecase }}\r\n      </p>\r\n      <p>\r\n        <strong>Hora de Início:</strong>\r\n        {{ studyGroup.hour }}\r\n      </p>\r\n      <mat-chip-set>\r\n        <mat-chip class=\"selected\" *ngFor=\"let day of studyGroup.daysOfWeek\">\r\n         <p class=\"selectedText\">{{day | titlecase}}</p>\r\n        </mat-chip>\r\n      </mat-chip-set>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        class=\"full_width\"\r\n        (click)=\"openDetalheDialog(studyGroup)\">\r\n        Detalhes\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n\r\n<!-- [routerLink]=\"['/study-group', studyGroup.id]\" -->\r\n", "import { catchError, map, Observable, of, tap } from 'rxjs';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { StudyGroup } from './study-group';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nconst AUTH_API = 'http://localhost:8080';\r\n\r\nconst httpOptions = {\r\n  headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n};\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StudyGroupService {\r\n  private studyGroupParam: any;\r\n  studyGroups: StudyGroup[] = [];\r\n  myStudyGroups: StudyGroup[] = [];\r\n  subjects: any[] = [];\r\n\r\n  private readonly http = inject(HttpClient);\r\n  constructor() {}\r\n\r\n  getStudyGroups(): Observable<any[]> {\r\n    return this.http\r\n      .post<any[]>(`${AUTH_API}/study-groups/filter`, {}, httpOptions)\r\n      .pipe(\r\n        map((studyGroups) => studyGroups.map(this.mappingStudyGroup)),\r\n        catchError((error) => {\r\n          console.error('Erro na chamada de API', error);\r\n          return of([]);\r\n        })\r\n      );\r\n  }\r\n\r\n  getStudyGroupsFind(studentId: number): Observable<any[]> {\r\n    const requestBody = { studentId };\r\n\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/filter`, requestBody, httpOptions)\r\n    .pipe(\r\n      map((studyGroups) => studyGroups.map(this.mappingStudyGroup)),\r\n      catchError((error) => {\r\n        console.error('Erro na chamada de API', error);\r\n        return of([]);\r\n      })\r\n    );;\r\n  }\r\n\r\n  public mappingStudyGroup(item: any): any {\r\n    const {\r\n      id,\r\n      description,\r\n      tutor,\r\n      subject,\r\n      students,\r\n      maxStudents,\r\n      meetingTime,\r\n      modality,\r\n      weekdays,\r\n      ownerId,\r\n      discordInviteUrl\r\n    } = item;\r\n\r\n    // const shortDescription =\r\n    //   description.length > 50 ? description.substr(0, 100) + '...' : description;\r\n\r\n    let countTutor = 0;\r\n    if (tutor) {\r\n      countTutor = 1;\r\n    }\r\n    const monitorText = `${countTutor}/${1}`;\r\n\r\n    const participantsCount = students.length;\r\n    const participantsText = `${participantsCount}/${maxStudents}`;\r\n    const mappedHour = meetingTime.substr(0, 5);\r\n\r\n    let mappedModality = '';\r\n    switch (modality) {\r\n      case 'REMOTE':\r\n        mappedModality = 'remoto';\r\n        break;\r\n      case 'PRESENCIAL':\r\n        mappedModality = 'presencial';\r\n        break;\r\n      default:\r\n        mappedModality = 'híbrido';\r\n    }\r\n\r\n    return {\r\n      id: id,\r\n      title: subject.name,\r\n      code: subject.code,\r\n      ownerId: ownerId,\r\n      shortDescription: description,\r\n      modality: mappedModality,\r\n      hour: mappedHour,\r\n      monitor: monitorText,\r\n      participants: participantsText,\r\n      students: students,\r\n      daysOfWeek: weekdays.map((day: { name: string }) =>\r\n        day.name.toLowerCase().substring(0, 3)\r\n      ),\r\n      discordInviteUrl: discordInviteUrl\r\n    };\r\n  }\r\n\r\n  joinGroupService(groupId: number,id: number){\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/${groupId}/students/${id}/join`, {}, httpOptions)\r\n  }\r\n\r\n  leaveGroupService(groupId: number,id: number){\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/${groupId}/students/${id}/leave`, {}, httpOptions)\r\n  }\r\n\r\n  getSubjects(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${AUTH_API}/subjects`);\r\n  }\r\n\r\n  getStudyGroupId(studyGroupId: number): Observable<any[]> {\r\n    return this.http.get<any[]>(`${AUTH_API}/study-groups/${studyGroupId}`);\r\n  }\r\n\r\n  createStudyGroup(studyGroupData: any): Observable<any> {\r\n    return this.http.post<any>(`${AUTH_API}/study-groups`, studyGroupData);\r\n  }\r\n\r\n  editStudyGroup(studyGroupData: any, groupId: number): Observable<any> {\r\n    return this.http.put<any>(`${AUTH_API}/study-groups/${groupId}`, studyGroupData);\r\n  }\r\n\r\n  setStudyGroup(data: any) {\r\n    this.studyGroupParam = data;\r\n  }\r\n\r\n  getStudyGroup() {\r\n    return this.studyGroupParam;\r\n  }\r\n}\r\n", "import { CommonModule, NgFor } from '@angular/common';\r\nimport { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { ReactiveFormsModule, FormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatIconButton, MatButton } from '@angular/material/button';\r\nimport { MatChipListbox, MatChipOption } from '@angular/material/chips';\r\nimport { MatOption } from '@angular/material/core';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatSelect } from '@angular/material/select';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-study-update-group',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatToolbar,\r\n    MatIconButton,\r\n    MatIcon,\r\n    MatFormField,\r\n    MatLabel,\r\n    MatSelect,\r\n    FormsModule,\r\n    NgFor,\r\n    MatOption,\r\n    MatChipListbox,\r\n    MatChipOption,\r\n    MatInput,\r\n    MatButton,\r\n    MatAutocompleteModule,\r\n    ToastModule,\r\n    ProgressSpinnerModule\r\n  ],\r\n  templateUrl: './study-update-group.component.html',\r\n  styleUrl: './study-update-group.component.scss',\r\n  providers: [MessageService]\r\n})\r\nexport class StudyUpdateGroupComponent implements OnInit {\r\n  formulario!: UntypedFormGroup;\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  loading: boolean = false;\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n\r\n  protected daysOfWeek: { id: number; name: string }[] = [\r\n    { id: 1, name: 'Dom' },\r\n    { id: 2, name: 'Seg' },\r\n    { id: 3, name: 'Ter' },\r\n    { id: 4, name: 'Qua' },\r\n    { id: 5, name: 'Qui' },\r\n    { id: 6, name: 'Sex' },\r\n    { id: 7, name: 'Sáb' },\r\n  ];\r\n\r\n  protected modalities: { value: string; viewValue: string }[] = [\r\n    { value: 'presencial', viewValue: 'Presencial' },\r\n    { value: 'online', viewValue: 'Online' },\r\n    { value: 'hibrido', viewValue: 'Híbrido' },\r\n  ];\r\n\r\n  protected selectedModality: String | null = null;\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private messageService: MessageService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private builder: UntypedFormBuilder) {\r\n    this.formulario = this.builder.group({\r\n      id: [''],\r\n      title: [''],\r\n      description: [''],\r\n      campoOculto: [''],\r\n      subject: [''],\r\n      maxStudents: [''],\r\n      meetingTime: [''],\r\n      modality: [\"REMOTE\"],\r\n      weekdays: [[]]\r\n    })\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.service.getSubjects().subscribe((dados) => {\r\n      this.service.subjects = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n\r\n    this.route.queryParams.subscribe(params => {\r\n      const groupId = params['id'];\r\n\r\n      this.service.getStudyGroupId(groupId).subscribe((dados: any) => {\r\n        this.formulario.patchValue(dados)\r\n        this.formulario.get('campoOculto')?.patchValue(dados.subject.code + ' - ' + dados.subject.name);\r\n\r\n        const selectedDays = dados.weekdays.map((day: { id: number; name: string }) => {\r\n          return this.daysOfWeek.find(d => d.id === day.id);\r\n        });\r\n        this.formulario.get('weekdays')?.patchValue(selectedDays);\r\n      })\r\n    });\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.subjects.filter(option =>\r\n      option.id.toString().includes(filterValue) || option.name.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  onOptionSelected(option: any): void {\r\n    this.formulario.get('subject')?.patchValue(option);\r\n    this.formulario.get('campoOculto')?.patchValue(option.code + ' - ' + option.name);\r\n  }\r\n\r\n  editGroups(){\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    const id = Number(idUsuario);\r\n    const title = this.formulario?.value.subject.code + \" \" + this.formulario?.value.subject.name;\r\n\r\n    const groupId = this.formulario?.value.id;\r\n\r\n    const studyGroupData = {\r\n      id: this.formulario?.value.id,\r\n      title: title,\r\n      description: this.formulario?.value.description,\r\n      userId: id,\r\n      ownerId: id,\r\n      subject: this.formulario?.value.subject,\r\n      weekdays: this.formulario?.value.weekdays,\r\n      meetingTime: this.formulario?.value.meetingTime,\r\n      maxStudents: this.formulario?.value.maxStudents,\r\n      modality: this.formulario?.value.modality\r\n    };\r\n\r\n    this.loading = true;\r\n\r\n    this.service.editStudyGroup(studyGroupData, groupId).subscribe(\r\n      response => {\r\n        console.log('Grupo de estudo editado com sucesso:', response);\r\n\r\n        this.snackBar.open(\r\n          'Grupo de estudo editado com sucesso!',\r\n          'X',\r\n          { duration: 2500 }\r\n        );\r\n        this.service.setStudyGroup(response);\r\n        this.router.navigate([`/detail/${response.id}`]);\r\n      },\r\n      error => {\r\n        this.loading = false;\r\n        console.error('Erro ao criar grupo de estudo:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n}\r\n", "<div class=\"filter-container\">\r\n  <form [formGroup]=\"formulario\" >\r\n    <div class=\"row\">\r\n      <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n        <mat-label>Disciplina</mat-label>\r\n        <input #input matInput formControlName=\"campoOculto\" class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n        <mat-autocomplete requireSelection #auto=\"matAutocomplete\" (optionSelected)=\"onOptionSelected($event.option.value)\">\r\n          <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option\">\r\n          <!-- <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.name\"> -->\r\n            ({{ option.code }}) {{ option.name }}\r\n          </mat-option>\r\n        </mat-autocomplete>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <mat-form-field class=\"input100\">\r\n      <mat-label>Descrição</mat-label>\r\n      <textarea matInput formControlName=\"description\"></textarea>\r\n    </mat-form-field>\r\n\r\n    <div class=\"row\">\r\n      <mat-form-field>\r\n        <mat-label>Número de Alunos</mat-label>\r\n        <mat-select formControlName=\"maxStudents\">\r\n          <mat-option [value]=\"2\">2</mat-option>\r\n          <mat-option [value]=\"3\">3</mat-option>\r\n          <mat-option [value]=\"4\">4</mat-option>\r\n          <mat-option [value]=\"5\">5</mat-option>\r\n          <mat-option [value]=\"6\">6</mat-option>\r\n        </mat-select>\r\n      </mat-form-field>\r\n\r\n      <mat-form-field class=\"margin input30\">\r\n        <mat-label>Hora de Início</mat-label>\r\n        <input matInput type=\"time\" formControlName=\"meetingTime\"/>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <h2>Dias da Semana</h2>\r\n    <mat-chip-listbox formControlName=\"weekdays\" multiple>\r\n      <mat-chip-option *ngFor=\"let day of daysOfWeek\" [value]=\"day\">\r\n        {{ day.name }}\r\n      </mat-chip-option>\r\n    </mat-chip-listbox>\r\n\r\n    <div class=\"allCenter\">\r\n      <p-toast />\r\n\r\n      @if(loading === true){\r\n        <p-progressSpinner ariaLabel=\"loading\" />\r\n      } @else {\r\n        <button mat-raised-button color=\"primary\" class=\"input100\" (click)=\"editGroups()\">Atualizar Grupo</button>\r\n      }\r\n    </div>\r\n  </form>\r\n</div>\r\n", "export const environment = {\r\n    development: true,\r\n    encodedApiUrl: 'http%3A%2F%2Flocalhost%3A8080',\r\n    // authApi: '/api'\r\n    authApi: ''\r\n};", "import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\n\r\nimport { importProvidersFrom } from '@angular/core';\r\nimport { AppComponent } from './app/app.component';\r\nimport { SharedModule } from './app/shared/shared.module';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { provideNoopAnimations } from '@angular/platform-browser/animations';\r\nimport { withInterceptorsFromDi, provideHttpClient } from '@angular/common/http';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BrowserModule, bootstrapApplication } from '@angular/platform-browser';\r\nimport { AngularMaterialModule } from './app/angular-material.module';\r\nimport { AppRoutingModule } from './app/app-routing.module';\r\nimport { httpInterceptorProviders } from './app/core/helpers/http.interceptor';\r\n\r\n\r\nbootstrapApplication(AppComponent, {\r\n    providers: [\r\n        importProvidersFrom(AppRoutingModule, AngularMaterialModule, BrowserModule, FormsModule, ReactiveFormsModule, MatCardModule, SharedModule),\r\n        httpInterceptorProviders,\r\n        provideHttpClient(withInterceptorsFromDi()),\r\n        provideNoopAnimations(),\r\n    ]\r\n})\r\n  .catch(err => console.error(err));\r\n"], "names": ["CommonModule", "OverlayModule", "CdkTreeModule", "PortalModule", "MatAutocompleteModule", "MatButtonModule", "MatButtonToggleModule", "MatCardModule", "MatCheckboxModule", "MatChipsModule", "MatRippleModule", "MatDividerModule", "MatExpansionModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatListModule", "MatMenuModule", "MatPaginatorModule", "MatProgressSpinnerModule", "MatSelectModule", "MatSidenavModule", "MatSnackBarModule", "MatSortModule", "MatTableModule", "MatTabsModule", "MatToolbarModule", "MatTreeModule", "MatBadgeModule", "MatGridListModule", "MatRadioModule", "MatDatepickerModule", "MatTooltipModule", "materialModules", "AngularMaterialModule", "_", "_2", "_3", "imports", "exports", "RouterModule", "RegisterComponent", "LoginComponent", "EmailConfirmComponent", "StudyGroupSearchBarComponent", "PasswordRecoveryComponent", "<PERSON>th<PERSON><PERSON>", "loggedInGuard", "StudyGroupDetailComponent", "StudyCreateGroupComponent", "MyStudyGroupComponent", "StudyUpdateGroupComponent", "StudyGroupAssociateComponent", "routes", "path", "component", "canActivate", "children", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "i1", "ChangeDetectorRef", "inject", "AuthService", "Router", "RouterLink", "RouterOutlet", "MatNavList", "MatListItem", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatIconButton", "MatToolbar", "MatMenu", "MatMenuItem", "MatSnackBar", "NavigationServiceService", "i0", "ɵɵelementStart", "ɵɵlistener", "AppComponent_Conditional_3_Conditional_0_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "navigateToSearch", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AppComponent_Conditional_3_Conditional_0_Template", "ɵɵconditional", "showBackIcon", "router", "url", "ɵɵadvance", "ɵɵtextInterpolate1", "user", "name", "AppComponent_Conditional_8_Template_button_click_2_listener", "_r3", "logout", "AppComponent_Conditional_8_Template_button_click_7_listener", "snav_r4", "ɵɵreference", "toggle", "AppComponent_Conditional_12_Conditional_1_Template_a_click_0_listener", "_r6", "close", "AppComponent_Conditional_12_Conditional_1_Template_a_click_2_listener", "AppComponent_Conditional_12_Conditional_1_Template_a_click_4_listener", "AppComponent_Conditional_12_Conditional_1_Template", "AppComponent_Conditional_12_Template_a_click_2_listener", "_r5", "AppComponent", "constructor", "location", "appName", "title", "isLoggedIn", "undefined", "snackBar", "authService", "cdr", "navigationService", "ngOnInit", "subscribe", "getUser", "detectChanges", "events", "startsWith", "next", "data", "console", "log", "navigateByUrl", "open", "duration", "error", "navigate", "sidenav", "ɵɵdirectiveInject", "Location", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "AppComponent_Conditional_3_Template", "ɵɵelement", "AppComponent_Conditional_8_Template", "AppComponent_Conditional_12_Template", "AppComponent_Conditional_13_Template", "ɵɵtextInterpolate", "styles", "route", "message", "queryParams", "params", "token", "confirmEmail", "err", "ActivatedRoute", "i2", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "EmailConfirmComponent_Template", "FormBuilder", "Validators", "FormsModule", "ReactiveFormsModule", "MatButton", "<PERSON><PERSON><PERSON><PERSON>", "MatInput", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatCard", "MatCardTitle", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "fb", "errorMessage", "loginForm", "group", "email", "required", "password", "onSubmit", "valid", "body", "controls", "value", "login", "idUsuario", "id", "toString", "localStorage", "setItem", "isDiscordAssociate", "consts", "LoginComponent_Template", "LoginComponent_Template_form_ngSubmit_5_listener", "ɵɵproperty", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "PasswordRecoveryComponent_Template", "isLogged", "signupForm", "register", "onSuccess", "onError", "reset", "reloadPage", "window", "reload", "RegisterComponent_Template", "RegisterComponent_Template_form_ngSubmit_5_listener", "HTTP_INTERCEPTORS", "HttpRequestInterceptor", "intercept", "req", "clone", "withCredentials", "handle", "factory", "ɵfac", "httpInterceptorProviders", "provide", "useClass", "multi", "HttpClient", "HttpHeaders", "catchError", "tap", "StorageService", "AUTH_API", "httpOptions", "headers", "http", "storageService", "post", "concat", "pipe", "saveUser", "clear", "get", "providedIn", "BehaviorSubject", "of", "SIGNED_USER", "<PERSON><PERSON><PERSON>", "hasSignedUser", "restoreLocalStorage", "removeItem", "JSON", "stringify", "signedUserString", "getItem", "Error", "parse", "asObservable", "signedUserStr", "signedUser<PERSON>bj", "_route", "_state", "b", "discordAssociateGuard", "signed", "nav_r3", "HeaderComponent", "fillerNav", "Array", "from", "length", "i", "HeaderComponent_Template", "HeaderComponent_Template_button_click_2_listener", "snav_r2", "ɵɵrepeaterCreate", "HeaderComponent_For_13_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "SharedModule", "<PERSON><PERSON><PERSON>", "MatCheckbox", "StudyGroupSearchItemComponent", "option_r2", "code", "ɵɵtextInterpolate2", "groups_r3", "service", "options", "selectedDays", "Set", "selected<PERSON>our", "Number", "getStudyGroupsFind", "dados", "myStudyGroups", "filteredOptions", "slice", "filter", "filterValue", "input", "nativeElement", "toLowerCase", "option", "includes", "applyFilters", "codeFilter", "titleFilter", "split", "map", "part", "trim", "filterByDayOfWeek", "filterByHour", "clearFilters", "time", "checkboxes", "for<PERSON>ach", "checkbox", "checked", "daysOfWeek", "size", "some", "day", "has", "hour", "days", "delete", "add", "onHourChange", "event", "target", "StudyGroupService", "MyStudyGroupComponent_Query", "MyStudyGroupComponent_Template_input_input_5_listener", "MyStudyGroupComponent_Template_input_focus_5_listener", "MyStudyGroupComponent_mat_option_9_Template", "MyStudyGroupComponent_Template_button_click_10_listener", "MyStudyGroupComponent_Template_button_click_13_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_22_listener", "$event", "stopPropagation", "MyStudyGroupComponent_Template_mat_checkbox_click_24_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_26_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_28_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_30_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_32_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_34_listener", "MyStudyGroupComponent_Template_mat_form_field_click_41_listener", "MyStudyGroupComponent_Template_input_change_44_listener", "MyStudyGroupComponent_div_47_Template", "auto_r4", "menu_r5", "menuHora_r6", "i3", "MatMenuTrigger", "i4", "i5", "MatAutocomplete", "i6", "MatOption", "MatAutocompleteTrigger", "NavigationStart", "previousUrls", "currentUrl", "navigationStartEvent", "push", "getPreviousUrl", "navigateToPrevious", "previousUrl", "catch", "pop", "ɵɵinject", "MatChipListbox", "MatChipOption", "MatSelect", "ToastModule", "MessageService", "ProgressSpinnerModule", "day_r3", "messageService", "builder", "modalities", "viewValue", "selectedModality", "subscription", "formulario", "description", "campoOculto", "subject", "maxStudents", "meetingTime", "modality", "weekdays", "getSubjects", "subjects", "onOptionSelected", "patchValue", "createGroups", "studyGroupData", "ownerId", "createStudyGroup", "response", "mappedStudyGroup", "mappingStudyGroup", "setStudyGroup", "ngOnDestroy", "unsubscribe", "UntypedFormBuilder", "StudyCreateGroupComponent_Query", "StudyCreateGroupComponent_Template", "StudyCreateGroupComponent_Template_input_input_6_listener", "StudyCreateGroupComponent_Template_input_focus_6_listener", "StudyCreateGroupComponent_Template_mat_autocomplete_optionSelected_8_listener", "StudyCreateGroupComponent_mat_option_10_Template", "StudyCreateGroupComponent_mat_chip_option_37_Template", "StudyCreateGroupComponent_Template_button_click_40_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i7", "i8", "Toast", "environment", "encodedApiUrl", "associate", "https", "StudyGroupAssociateComponent_Template", "StudyGroupAssociateComponent_Template_button_click_8_listener", "MatChipSet", "MatChip", "MatCardSubtitle", "ɵɵpureFunction1", "_c0", "studyGroup", "day_r1", "_c1", "ɵɵpipeBind1", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template_button_click_0_listener", "_r4", "editGroup", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Template_button_click_0_listener", "openDiscord", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Template_button_click_4_listener", "leaveGroup", "discordInviteUrl", "isOwnerId", "StudyGroupDetailComponent_Conditional_21_Conditional_1_Template_button_click_0_listener", "joinGroup", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Template", "StudyGroupDetailComponent_Conditional_21_Conditional_1_Template", "userInGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "getStudyGroup", "callGroup", "panelClass", "joinGroupService", "leaveGroupService", "resposta", "idParam", "snapshot", "paramMap", "idDetail", "getStudyGroupId", "isArray", "students", "isStudentInGroup", "student", "isOwner", "ɵɵProvidersFeature", "StudyGroupDetailComponent_Template", "StudyGroupDetailComponent_mat_chip_19_Template", "StudyGroupDetailComponent_Conditional_20_Template", "StudyGroupDetailComponent_Conditional_21_Template", "shortDescription", "participants", "Ng<PERSON><PERSON>", "TitleCasePipe", "ProgressSpinner", "getStudyGroups", "studyGroups", "navigateCreate", "StudyGroupSearchBarComponent_Query", "StudyGroupSearchBarComponent_Template", "StudyGroupSearchBarComponent_Template_input_input_5_listener", "StudyGroupSearchBarComponent_Template_input_focus_5_listener", "StudyGroupSearchBarComponent_mat_option_9_Template", "StudyGroupSearchBarComponent_Template_button_click_10_listener", "StudyGroupSearchBarComponent_Template_button_click_13_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_23_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_25_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_27_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_29_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_31_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_33_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_35_listener", "StudyGroupSearchBarComponent_Template_mat_form_field_click_42_listener", "StudyGroupSearchBarComponent_Template_input_change_45_listener", "StudyGroupSearchBarComponent_Template_button_click_48_listener", "StudyGroupSearchBarComponent_div_52_Template", "MatCardHeader", "MatCardActions", "openDetalheDialog", "inputs", "StudyGroupSearchItemComponent_Template", "StudyGroupSearchItemComponent_mat_chip_22_Template", "StudyGroupSearchItemComponent_Template_button_click_24_listener", "ɵɵpropertyInterpolate", "MatTooltip", "studentId", "requestBody", "item", "tutor", "countTutor", "monitorText", "participantsCount", "participantsText", "mappedHour", "substr", "mappedModality", "monitor", "substring", "groupId", "studyGroupId", "editStudyGroup", "put", "studyGroupParam", "StudyUpdateGroupComponent_Conditional_41_Template_button_click_0_listener", "ctx_r4", "editGroups", "find", "d", "userId", "StudyUpdateGroupComponent_Query", "StudyUpdateGroupComponent_Template", "StudyUpdateGroupComponent_Template_input_input_6_listener", "StudyUpdateGroupComponent_Template_input_focus_6_listener", "StudyUpdateGroupComponent_Template_mat_autocomplete_optionSelected_8_listener", "StudyUpdateGroupComponent_mat_option_10_Template", "StudyUpdateGroupComponent_mat_chip_option_37_Template", "StudyUpdateGroupComponent_Conditional_40_Template", "StudyUpdateGroupComponent_Conditional_41_Template", "auto_r6", "i9", "development", "authApi", "importProvidersFrom", "provideNoopAnimations", "withInterceptorsFromDi", "provideHttpClient", "BrowserModule", "bootstrapApplication", "providers"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}