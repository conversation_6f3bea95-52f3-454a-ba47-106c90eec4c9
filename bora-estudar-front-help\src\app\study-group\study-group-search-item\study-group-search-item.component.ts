import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StudyGroup } from '../study-group';
import { Router, RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>on } from '@angular/material/button';
import { Ng<PERSON><PERSON>, TitleCasePipe } from '@angular/common';
import { MatChipSet, MatChip } from '@angular/material/chips';
import { <PERSON><PERSON>ard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';
import { StudyGroupService } from '../study-group.service';

@Component({
    selector: 'app-study-group-search-item',
    templateUrl: './study-group-search-item.component.html',
    styleUrls: ['./study-group-search-item.component.scss'],
    standalone: true,
    imports: [
        <PERSON><PERSON><PERSON>,
        Mat<PERSON>ardHeader,
        Mat<PERSON>ardTitle,
        <PERSON><PERSON>ardSubtitle,
        <PERSON><PERSON><PERSON><PERSON><PERSON>nt,
        MatChipSet,
        <PERSON><PERSON><PERSON>,
        Mat<PERSON><PERSON>,
        <PERSON><PERSON>ardActions,
        MatButton,
        RouterLink,
        TitleCasePipe,
        MatTooltipModule,
    ],
})
export class StudyGroupSearchItemComponent {
  @Input() studyGroup!: StudyGroup;

  constructor(
    public service: StudyGroupService,
    private router: Router) {}

  openDetalheDialog(studyGroup: any): void {
    this.service.setStudyGroup(studyGroup);
    this.router.navigate([`/detail/${studyGroup.id}`]);

    // let dialogRef = this.dialog.open(StudyGroupDetailComponent, {
    //   maxWidth: '100vw',
    //   maxHeight: '100vh',
    //   height: '100%',
    //   width: '100%',
    //   data: { id: id }
    // });
  }
}
