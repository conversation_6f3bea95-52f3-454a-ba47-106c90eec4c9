{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/button';\nimport { <PERSON><PERSON><PERSON>, TitleCasePipe } from '@angular/common';\nimport { MatChipSet, MatChip } from '@angular/material/chips';\nimport { MatCard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../study-group.service\";\nimport * as i2 from \"@angular/router\";\nfunction StudyGroupSearchItemComponent_mat_chip_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 4)(1, \"p\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, day_r1));\n  }\n}\nexport class StudyGroupSearchItemComponent {\n  constructor(service, router) {\n    this.service = service;\n    this.router = router;\n  }\n  openDetalheDialog(studyGroup) {\n    this.service.setStudyGroup(studyGroup);\n    this.router.navigate([`/detail/${studyGroup.id}`]);\n    // let dialogRef = this.dialog.open(StudyGroupDetailComponent, {\n    //   maxWidth: '100vw',\n    //   maxHeight: '100vh',\n    //   height: '100%',\n    //   width: '100%',\n    //   data: { id: id }\n    // });\n  }\n  static #_ = this.ɵfac = function StudyGroupSearchItemComponent_Factory(t) {\n    return new (t || StudyGroupSearchItemComponent)(i0.ɵɵdirectiveInject(i1.StudyGroupService), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StudyGroupSearchItemComponent,\n    selectors: [[\"app-study-group-search-item\"]],\n    inputs: {\n      studyGroup: \"studyGroup\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 26,\n    vars: 8,\n    consts: [[1, \"container\"], [1, \"study_group_item_title\"], [\"class\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full_width\", 3, \"click\"], [1, \"selected\"], [1, \"selectedText\"]],\n    template: function StudyGroupSearchItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\", 1);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\")(9, \"strong\");\n        i0.ɵɵtext(10, \"Alunos:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\")(13, \"strong\");\n        i0.ɵɵtext(14, \"Modalidade:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"titlecase\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"p\")(18, \"strong\");\n        i0.ɵɵtext(19, \"Hora de In\\u00EDcio:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"mat-chip-set\");\n        i0.ɵɵtemplate(22, StudyGroupSearchItemComponent_mat_chip_22_Template, 4, 3, \"mat-chip\", 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"mat-card-actions\")(24, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function StudyGroupSearchItemComponent_Template_button_click_24_listener() {\n          return ctx.openDetalheDialog(ctx.studyGroup);\n        });\n        i0.ɵɵtext(25, \" Detalhes \");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.studyGroup.title);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.studyGroup.shortDescription);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", ctx.studyGroup.participants, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 6, ctx.studyGroup.modality), \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", ctx.studyGroup.hour, \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.studyGroup.daysOfWeek);\n      }\n    },\n    dependencies: [MatCard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatChipSet, NgFor, MatChip, MatCardActions, MatButton, TitleCasePipe],\n    styles: [\"@charset \\\"UTF-8\\\";\\nmat-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 320px; \\n\\n  height: auto;\\n  min-height: 320px; \\n\\n  margin: 12px; \\n\\n  padding: 16px;\\n  box-sizing: border-box;\\n  flex-shrink: 0; \\n\\n}\\n\\n\\n\\nmat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  padding-bottom: 8px;\\n}\\n\\n\\n\\n.study_group_item_title[_ngcontent-%COMP%] {\\n  font-size: 18px !important;\\n  font-weight: 500 !important;\\n  line-height: 1.3 !important;\\n  word-wrap: break-word !important;\\n  overflow-wrap: break-word !important;\\n  -webkit-hyphens: auto !important;\\n          hyphens: auto !important;\\n  margin-bottom: 8px !important;\\n  max-height: none !important;\\n  white-space: normal !important;\\n}\\n\\n\\n\\nmat-card-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px !important;\\n  line-height: 1.4 !important;\\n  word-wrap: break-word !important;\\n  overflow-wrap: break-word !important;\\n  margin-top: 4px !important;\\n  opacity: 0.7;\\n}\\n\\n\\n\\nmat-card-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  margin-top: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n\\n\\nmat-card-actions[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  padding-top: 8px;\\n}\\n\\n\\n\\nmat-card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  word-wrap: break-word;\\n}\\n\\n\\n\\nmat-chip-set[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n}\\n\\nmat-chip[_ngcontent-%COMP%] {\\n  margin: 3px;\\n  font-size: 12px;\\n}\\n\\n.full_width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: 8px;\\n}\\n\\n.selected[_ngcontent-%COMP%] {\\n  background-color: #3f51b5 !important;\\n}\\n\\n.selectedText[_ngcontent-%COMP%] {\\n  color: white;\\n  margin: 0;\\n  font-size: 11px;\\n  padding: 2px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MatButton", "<PERSON><PERSON><PERSON>", "TitleCasePipe", "MatChipSet", "MatChip", "MatCard", "MatCardHeader", "MatCardTitle", "MatCardSubtitle", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardActions", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "day_r1", "StudyGroupSearchItemComponent", "constructor", "service", "router", "openDetalheDialog", "studyGroup", "setStudyGroup", "navigate", "id", "_", "ɵɵdirectiveInject", "i1", "StudyGroupService", "i2", "Router", "_2", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StudyGroupSearchItemComponent_Template", "rf", "ctx", "ɵɵtemplate", "StudyGroupSearchItemComponent_mat_chip_22_Template", "ɵɵlistener", "StudyGroupSearchItemComponent_Template_button_click_24_listener", "title", "shortDescription", "ɵɵtextInterpolate1", "participants", "modality", "hour", "ɵɵproperty", "daysOfWeek", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\study-group\\study-group-search-item\\study-group-search-item.component.ts", "C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\study-group\\study-group-search-item\\study-group-search-item.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { StudyGroup } from '../study-group';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { MatButton } from '@angular/material/button';\r\nimport { <PERSON><PERSON><PERSON>, TitleCasePipe } from '@angular/common';\r\nimport { MatChipSet, MatChip } from '@angular/material/chips';\r\nimport { Mat<PERSON>ard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\r\nimport { StudyGroupService } from '../study-group.service';\r\n\r\n@Component({\r\n    selector: 'app-study-group-search-item',\r\n    templateUrl: './study-group-search-item.component.html',\r\n    styleUrls: ['./study-group-search-item.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        Mat<PERSON>ard,\r\n        MatCardHeader,\r\n        MatCardTitle,\r\n        MatCardSubtitle,\r\n        MatCardContent,\r\n        MatChipSet,\r\n        NgFor,\r\n        MatC<PERSON>,\r\n        <PERSON><PERSON>ard<PERSON><PERSON>,\r\n        Mat<PERSON>utton,\r\n        RouterLink,\r\n        TitleCasePipe,\r\n    ],\r\n})\r\nexport class StudyGroupSearchItemComponent {\r\n  @Input() studyGroup!: StudyGroup;\r\n\r\n  constructor(\r\n    public service: StudyGroupService,\r\n    private router: Router) {}\r\n\r\n  openDetalheDialog(studyGroup: any): void {\r\n    this.service.setStudyGroup(studyGroup);\r\n    this.router.navigate([`/detail/${studyGroup.id}`]);\r\n\r\n    // let dialogRef = this.dialog.open(StudyGroupDetailComponent, {\r\n    //   maxWidth: '100vw',\r\n    //   maxHeight: '100vh',\r\n    //   height: '100%',\r\n    //   width: '100%',\r\n    //   data: { id: id }\r\n    // });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title class=\"study_group_item_title\">{{ studyGroup.title }}</mat-card-title>\r\n      <mat-card-subtitle>{{ studyGroup.shortDescription }}</mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <!-- <p>\r\n        <strong>Monitor:</strong>\r\n        {{ studyGroup.monitor }}\r\n      </p> -->\r\n      <p>\r\n        <strong>Alunos:</strong>\r\n        {{ studyGroup.participants }}\r\n      </p>\r\n      <p>\r\n        <strong>Modalidade:</strong>\r\n        {{ studyGroup.modality | titlecase }}\r\n      </p>\r\n      <p>\r\n        <strong><PERSON><PERSON> de <PERSON>cio:</strong>\r\n        {{ studyGroup.hour }}\r\n      </p>\r\n      <mat-chip-set>\r\n        <mat-chip class=\"selected\" *ngFor=\"let day of studyGroup.daysOfWeek\">\r\n         <p class=\"selectedText\">{{day | titlecase}}</p>\r\n        </mat-chip>\r\n      </mat-chip-set>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        class=\"full_width\"\r\n        (click)=\"openDetalheDialog(studyGroup)\">\r\n        Detalhes\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n\r\n<!-- [routerLink]=\"['/study-group', studyGroup.id]\" -->\r\n"], "mappings": "AAGA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,KAAK,EAAEC,aAAa,QAAQ,iBAAiB;AACtD,SAASC,UAAU,EAAEC,OAAO,QAAQ,yBAAyB;AAC7D,SAASC,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,QAAQ,wBAAwB;;;;;;ICmBrHC,EADD,CAAAC,cAAA,kBAAqE,WAC5C;IAAAD,EAAA,CAAAE,MAAA,GAAmB;;IAC5CF,EAD4C,CAAAG,YAAA,EAAI,EACrC;;;;IADcH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,EAAmB;;;ADIpD,OAAM,MAAOC,6BAA6B;EAGxCC,YACSC,OAA0B,EACzBC,MAAc;IADf,KAAAD,OAAO,GAAPA,OAAO;IACN,KAAAC,MAAM,GAANA,MAAM;EAAW;EAE3BC,iBAAiBA,CAACC,UAAe;IAC/B,IAAI,CAACH,OAAO,CAACI,aAAa,CAACD,UAAU,CAAC;IACtC,IAAI,CAACF,MAAM,CAACI,QAAQ,CAAC,CAAC,WAAWF,UAAU,CAACG,EAAE,EAAE,CAAC,CAAC;IAElD;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAAC,QAAAC,CAAA,G;qBAlBUT,6BAA6B,EAAAR,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAApB,EAAA,CAAAkB,iBAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7Bf,6BAA6B;IAAAgB,SAAA;IAAAC,MAAA;MAAAZ,UAAA;IAAA;IAAAa,UAAA;IAAAC,QAAA,GAAA3B,EAAA,CAAA4B,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1BpClC,EAHN,CAAAC,cAAA,aAAuB,eACX,sBACS,wBACgC;QAAAD,EAAA,CAAAE,MAAA,GAAsB;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QACtFH,EAAA,CAAAC,cAAA,wBAAmB;QAAAD,EAAA,CAAAE,MAAA,GAAiC;QACtDF,EADsD,CAAAG,YAAA,EAAoB,EACxD;QAOdH,EANJ,CAAAC,cAAA,uBAAkB,QAKb,aACO;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACxBH,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEFH,EADF,CAAAC,cAAA,SAAG,cACO;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC5BH,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEFH,EADF,CAAAC,cAAA,SAAG,cACO;QAAAD,EAAA,CAAAE,MAAA,4BAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAChCH,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,oBAAc;QACZD,EAAA,CAAAoC,UAAA,KAAAC,kDAAA,sBAAqE;QAIzErC,EADE,CAAAG,YAAA,EAAe,EACE;QAGjBH,EADF,CAAAC,cAAA,wBAAkB,iBAK0B;QAAxCD,EAAA,CAAAsC,UAAA,mBAAAC,gEAAA;UAAA,OAASJ,GAAA,CAAAvB,iBAAA,CAAAuB,GAAA,CAAAtB,UAAA,CAA6B;QAAA,EAAC;QACvCb,EAAA,CAAAE,MAAA,kBACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACQ,EACV,EACP;;;QArC+CH,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,iBAAA,CAAA8B,GAAA,CAAAtB,UAAA,CAAA2B,KAAA,CAAsB;QAClDxC,EAAA,CAAAI,SAAA,GAAiC;QAAjCJ,EAAA,CAAAK,iBAAA,CAAA8B,GAAA,CAAAtB,UAAA,CAAA4B,gBAAA,CAAiC;QASlDzC,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAA0C,kBAAA,MAAAP,GAAA,CAAAtB,UAAA,CAAA8B,YAAA,MACF;QAGE3C,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAA0C,kBAAA,MAAA1C,EAAA,CAAAM,WAAA,QAAA6B,GAAA,CAAAtB,UAAA,CAAA+B,QAAA,OACF;QAGE5C,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAA0C,kBAAA,MAAAP,GAAA,CAAAtB,UAAA,CAAAgC,IAAA,MACF;QAE6C7C,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAA8C,UAAA,YAAAX,GAAA,CAAAtB,UAAA,CAAAkC,UAAA,CAAwB;;;mBDTnErD,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,cAAc,EACdN,UAAU,EACVF,KAAK,EACLG,OAAO,EACPM,cAAc,EACdV,SAAS,EAETE,aAAa;IAAAyD,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}