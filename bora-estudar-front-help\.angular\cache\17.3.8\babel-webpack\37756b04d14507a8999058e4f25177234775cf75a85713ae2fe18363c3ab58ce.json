{"ast": null, "code": "import { MatTooltipModule } from '@angular/material/tooltip';\nimport { <PERSON>Button } from '@angular/material/button';\nimport { <PERSON><PERSON><PERSON>, TitleCasePipe } from '@angular/common';\nimport { MatChipSet, MatChip } from '@angular/material/chips';\nimport { Mat<PERSON>ard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../study-group.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/tooltip\";\nfunction StudyGroupSearchItemComponent_mat_chip_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 5)(1, \"p\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, day_r1));\n  }\n}\nexport let StudyGroupSearchItemComponent = /*#__PURE__*/(() => {\n  class StudyGroupSearchItemComponent {\n    constructor(service, router) {\n      this.service = service;\n      this.router = router;\n    }\n    openDetalheDialog(studyGroup) {\n      this.service.setStudyGroup(studyGroup);\n      this.router.navigate([`/detail/${studyGroup.id}`]);\n      // let dialogRef = this.dialog.open(StudyGroupDetailComponent, {\n      //   maxWidth: '100vw',\n      //   maxHeight: '100vh',\n      //   height: '100%',\n      //   width: '100%',\n      //   data: { id: id }\n      // });\n    }\n    static #_ = this.ɵfac = function StudyGroupSearchItemComponent_Factory(t) {\n      return new (t || StudyGroupSearchItemComponent)(i0.ɵɵdirectiveInject(i1.StudyGroupService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudyGroupSearchItemComponent,\n      selectors: [[\"app-study-group-search-item\"]],\n      inputs: {\n        studyGroup: \"studyGroup\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 26,\n      vars: 10,\n      consts: [[1, \"container\"], [1, \"study_group_item_title\", \"limited-title\", 3, \"matTooltip\"], [1, \"limited-subtitle\", 3, \"matTooltip\"], [\"class\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full_width\", 3, \"click\"], [1, \"selected\"], [1, \"selectedText\"]],\n      template: function StudyGroupSearchItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\", 1);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\", 2);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\")(9, \"strong\");\n          i0.ɵɵtext(10, \"Alunos:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\")(13, \"strong\");\n          i0.ɵɵtext(14, \"Modalidade:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"titlecase\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\")(18, \"strong\");\n          i0.ɵɵtext(19, \"Hora de In\\u00EDcio:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-chip-set\");\n          i0.ɵɵtemplate(22, StudyGroupSearchItemComponent_mat_chip_22_Template, 4, 3, \"mat-chip\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"mat-card-actions\")(24, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function StudyGroupSearchItemComponent_Template_button_click_24_listener() {\n            return ctx.openDetalheDialog(ctx.studyGroup);\n          });\n          i0.ɵɵtext(25, \" Detalhes \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx.studyGroup.title);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.studyGroup.title);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx.studyGroup.shortDescription);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.studyGroup.shortDescription);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.studyGroup.participants, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 8, ctx.studyGroup.modality), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.studyGroup.hour, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.studyGroup.daysOfWeek);\n        }\n      },\n      dependencies: [MatCard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatChipSet, NgFor, MatChip, MatCardActions, MatButton, TitleCasePipe, MatTooltipModule, i3.MatTooltip],\n      styles: [\"mat-card[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:320px;height:345px;max-height:345px;margin:12px;padding:16px;box-sizing:border-box;flex-shrink:0}.limited-title[_ngcontent-%COMP%]{display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;max-height:3.2em;line-height:1.6em;white-space:normal}.limited-subtitle[_ngcontent-%COMP%]{display:-webkit-box;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;max-height:2.6em;line-height:1.6em;white-space:normal}mat-chip[_ngcontent-%COMP%]{margin:5px}.full_width[_ngcontent-%COMP%]{width:100%}.selected[_ngcontent-%COMP%]{background-color:#3f51b5!important}.selectedText[_ngcontent-%COMP%]{color:#fff;margin-top:12px}\"]\n    });\n  }\n  return StudyGroupSearchItemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}