{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { RegisterComponent } from './auth/components/register/register.component';\nimport { LoginComponent } from './auth/components/login/login.component';\nimport { EmailConfirmComponent } from './auth/components/email-confirm/email-confirm.component';\nimport { StudyGroupSearchBarComponent } from './study-group/study-group-search-bar/study-group-search.component';\nimport { PasswordRecoveryComponent } from './auth/components/password-recovery/password-recovery.component';\nimport { authGuard, loggedInGuard } from './core/security/guard/auth.guard';\nimport { StudyGroupDetailComponent } from './study-group/study-group-detail/study-group-detail.component';\nimport { StudyCreateGroupComponent } from './study-group/study-create-group/study-create-group.component';\nimport { MyStudyGroupComponent } from './study-group/my-study-group/my-study-group.component';\nimport { StudyUpdateGroupComponent } from './study-group/study-update-group/study-update-group.component';\nimport { StudyGroupAssociateComponent } from './study-group/study-group-associate/study-group-associate.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'password-recovery',\n  component: PasswordRecoveryComponent,\n  canActivate: [loggedInGuard]\n}, {\n  path: 'login',\n  component: LoginComponent,\n  canActivate: [loggedInGuard]\n},\n// prettier-ignore\n{\n  path: 'register',\n  component: RegisterComponent,\n  canActivate: [loggedInGuard]\n},\n// prettier-ignore\n{\n  path: 'confirm',\n  component: EmailConfirmComponent,\n  canActivate: [loggedInGuard]\n}, {\n  path: '',\n  component: LoginComponent,\n  canActivate: [loggedInGuard]\n}, {\n  path: '',\n  canActivate: [authGuard],\n  children: [{\n    path: 'search',\n    component: StudyGroupSearchBarComponent\n  }, {\n    path: 'create',\n    component: StudyCreateGroupComponent\n  }, {\n    path: 'detail/:groupId',\n    component: StudyGroupDetailComponent\n  }, {\n    path: 'my-study-group',\n    component: MyStudyGroupComponent\n  }, {\n    path: 'edit',\n    component: StudyUpdateGroupComponent\n  }, {\n    path: 'associate',\n    component: StudyGroupAssociateComponent\n  }, {\n    path: '',\n    redirectTo: 'search',\n    pathMatch: 'full'\n  }]\n}];\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "RegisterComponent", "LoginComponent", "EmailConfirmComponent", "StudyGroupSearchBarComponent", "PasswordRecoveryComponent", "<PERSON>th<PERSON><PERSON>", "loggedInGuard", "StudyGroupDetailComponent", "StudyCreateGroupComponent", "MyStudyGroupComponent", "StudyUpdateGroupComponent", "StudyGroupAssociateComponent", "routes", "path", "component", "canActivate", "children", "redirectTo", "pathMatch", "AppRoutingModule", "_", "_2", "_3", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { RegisterComponent } from './auth/components/register/register.component';\r\nimport { LoginComponent } from './auth/components/login/login.component';\r\nimport { EmailConfirmComponent } from './auth/components/email-confirm/email-confirm.component';\r\nimport { StudyGroupSearchBarComponent } from './study-group/study-group-search-bar/study-group-search.component';\r\nimport { PasswordRecoveryComponent } from './auth/components/password-recovery/password-recovery.component';\r\nimport { authGuard, discordAssociateGuard, loggedInGuard } from './core/security/guard/auth.guard';\r\nimport { StudyGroupDetailComponent } from './study-group/study-group-detail/study-group-detail.component';\r\nimport { StudyCreateGroupComponent } from './study-group/study-create-group/study-create-group.component';\r\nimport { MyStudyGroupComponent } from './study-group/my-study-group/my-study-group.component';\r\nimport { StudyUpdateGroupComponent } from './study-group/study-update-group/study-update-group.component';\r\nimport { StudyGroupAssociateComponent } from './study-group/study-group-associate/study-group-associate.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'password-recovery',\r\n    component: PasswordRecoveryComponent,\r\n    canActivate: [loggedInGuard],\r\n  },\r\n  { path: 'login', component: LoginComponent, canActivate: [loggedInGuard] },\r\n  // prettier-ignore\r\n  { path: 'register', component: RegisterComponent, canActivate: [loggedInGuard]},\r\n  // prettier-ignore\r\n  { path: 'confirm', component: EmailConfirmComponent, canActivate: [loggedInGuard] },\r\n  {\r\n    path: '',\r\n    component: LoginComponent,\r\n    canActivate: [loggedInGuard],\r\n  },\r\n  {\r\n    path: '',\r\n    canActivate: [authGuard],\r\n    children: [\r\n      { path: 'search', component: StudyGroupSearchBarComponent },\r\n      { path: 'create', component: StudyCreateGroupComponent },\r\n      { path: 'detail/:groupId', component: StudyGroupDetailComponent },\r\n      { path: 'my-study-group', component: MyStudyGroupComponent },\r\n      { path: 'edit', component: StudyUpdateGroupComponent },\r\n      { path: 'associate', component: StudyGroupAssociateComponent },\r\n      { path: '', redirectTo: 'search', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,4BAA4B,QAAQ,mEAAmE;AAChH,SAASC,yBAAyB,QAAQ,iEAAiE;AAC3G,SAASC,SAAS,EAAyBC,aAAa,QAAQ,kCAAkC;AAClG,SAASC,yBAAyB,QAAQ,+DAA+D;AACzG,SAASC,yBAAyB,QAAQ,+DAA+D;AACzG,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,yBAAyB,QAAQ,+DAA+D;AACzG,SAASC,4BAA4B,QAAQ,qEAAqE;;;AAElH,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEV,yBAAyB;EACpCW,WAAW,EAAE,CAACT,aAAa;CAC5B,EACD;EAAEO,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEb,cAAc;EAAEc,WAAW,EAAE,CAACT,aAAa;AAAC,CAAE;AAC1E;AACA;EAAEO,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEd,iBAAiB;EAAEe,WAAW,EAAE,CAACT,aAAa;AAAC,CAAC;AAC/E;AACA;EAAEO,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEZ,qBAAqB;EAAEa,WAAW,EAAE,CAACT,aAAa;AAAC,CAAE,EACnF;EACEO,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEb,cAAc;EACzBc,WAAW,EAAE,CAACT,aAAa;CAC5B,EACD;EACEO,IAAI,EAAE,EAAE;EACRE,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBW,QAAQ,EAAE,CACR;IAAEH,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEX;EAA4B,CAAE,EAC3D;IAAEU,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEN;EAAyB,CAAE,EACxD;IAAEK,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAEP;EAAyB,CAAE,EACjE;IAAEM,IAAI,EAAE,gBAAgB;IAAEC,SAAS,EAAEL;EAAqB,CAAE,EAC5D;IAAEI,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEJ;EAAyB,CAAE,EACtD;IAAEG,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEH;EAA4B,CAAE,EAC9D;IAAEE,IAAI,EAAE,EAAE;IAAEI,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE;EAAM,CAAE;CAExD,CACF;AAMD,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cAHjBvB,YAAY,CAACwB,OAAO,CAACX,MAAM,CAAC,EAC5Bb,YAAY;EAAA;;;2EAEXoB,gBAAgB;IAAAK,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFjB3B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}