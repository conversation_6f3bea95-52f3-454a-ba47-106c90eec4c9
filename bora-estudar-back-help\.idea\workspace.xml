<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ce39dcc8-6814-471e-b125-446e2ff7c342" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/configurations/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/configurations/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/controllers/AuthDiscordController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/controllers/AuthDiscordController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-local.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-prod.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-prod.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/data.sql" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/data.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="Use Maven wrapper" />
        <option name="useMavenConfig" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectId" id="2uRKybPRrK7spGJJHRFNtkLtrcO" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "TODO_SCOPE": "All Places",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "adjustments",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/Documents/uff/tcc/github/monolito-bora-estudar/bora-estudar-back-help",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "spring.configuration.checksum": "801cc4595eee3c7fe82cc225d187a9eb",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "h2"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="BoraEstudarBackApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="local" />
      <module name="bora-estudar" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ufftcc.boraestudar.BoraEstudarBackApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ce39dcc8-6814-471e-b125-446e2ff7c342" name="Changes" comment="" />
      <created>1742206202413</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742206202413</updated>
      <workItem from="1742206203690" duration="105000" />
      <workItem from="1742776952532" duration="3951000" />
      <workItem from="1743350046523" duration="2484000" />
      <workItem from="1744330954833" duration="80000" />
      <workItem from="1744331048188" duration="1168000" />
      <workItem from="1745434158289" duration="6645000" />
      <workItem from="1748626974385" duration="19555000" />
      <workItem from="1750631630239" duration="4500000" />
      <workItem from="1750636485724" duration="1466000" />
      <workItem from="1750638003088" duration="1606000" />
      <workItem from="1751236544294" duration="20909000" />
      <workItem from="1751325596136" duration="31315000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/exceptions/handlers/SecurityExceptionHandler.java</url>
          <line>36</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/services/AuthService.java</url>
          <line>83</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/controllers/AuthDiscordController.java</url>
          <line>30</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/services/AuthDiscordService.java</url>
          <line>51</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ufftcc/boraestudar/controllers/StudyGroupController.java</url>
          <line>62</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>