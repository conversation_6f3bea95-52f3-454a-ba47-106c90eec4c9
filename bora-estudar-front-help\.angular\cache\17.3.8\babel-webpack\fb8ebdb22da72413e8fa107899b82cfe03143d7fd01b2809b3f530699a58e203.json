{"ast": null, "code": "import { <PERSON><PERSON><PERSON> } from '@angular/common';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { <PERSON><PERSON>utton } from '@angular/material/button';\nimport { MatCheckbox, MatCheckboxModule } from '@angular/material/checkbox';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>abel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { StudyGroupSearchItemComponent } from '../study-group-search-item/study-group-search-item.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../study-group.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/menu\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nconst _c0 = [\"input\"];\nconst _c1 = [\"time\"];\nfunction MyStudyGroupComponent_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.code + \" - \" + option_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" (\", option_r2.code, \") \", option_r2.title, \" \");\n  }\n}\nfunction MyStudyGroupComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-study-group-search-item\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groups_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"studyGroup\", groups_r3);\n  }\n}\nexport let MyStudyGroupComponent = /*#__PURE__*/(() => {\n  class MyStudyGroupComponent {\n    constructor(cdr, service, router) {\n      this.cdr = cdr;\n      this.service = service;\n      this.router = router;\n      this.options = [];\n      this.selectedDays = new Set();\n      this.selectedHour = '';\n    }\n    ngOnInit() {\n      const idUsuario = localStorage.getItem('idUsuario');\n      const id = Number(idUsuario);\n      this.service.getStudyGroupsFind(id).subscribe(dados => {\n        console.log('Dados carregados:', dados);\n        this.service.myStudyGroups = dados;\n        this.options = dados;\n        this.filteredOptions = this.options.slice();\n      });\n    }\n    filter() {\n      const filterValue = this.input.nativeElement.value.toLowerCase();\n      this.filteredOptions = this.service.myStudyGroups.filter(option => option.title.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue));\n    }\n    applyFilters() {\n      const filterValue = this.input.nativeElement.value.toLowerCase();\n      // Dividir o valor do filtro em partes, se necessário\n      const [codeFilter, titleFilter] = filterValue.split(' - ').map(part => part.trim());\n      const filter = this.service.myStudyGroups?.filter(option => this.filterByDayOfWeek(option) && this.filterByHour(option) && (option.code.toLowerCase().includes(codeFilter) || option.title.toLowerCase().includes(titleFilter))) || [];\n      this.options = [...filter];\n      this.cdr.detectChanges();\n    }\n    clearFilters() {\n      this.input.nativeElement.value = '';\n      this.time.nativeElement.value = '';\n      this.checkboxes.forEach(checkbox => checkbox.checked = false);\n      this.cdr.detectChanges();\n    }\n    filterByDayOfWeek(option) {\n      if (!option.daysOfWeek || this.selectedDays.size === 0) {\n        return true; // Sem filtro de dia da semana ou dados não definidos\n      }\n      return option.daysOfWeek.some(day => this.selectedDays.has(day.toLowerCase()));\n    }\n    filterByHour(option) {\n      if (!this.selectedHour) {\n        return true; // Sem filtro de horário\n      }\n      return option.hour >= this.selectedHour;\n    }\n    days(day) {\n      if (this.selectedDays.has(day)) {\n        this.selectedDays.delete(day);\n      } else {\n        this.selectedDays.add(day);\n      }\n    }\n    onHourChange(event) {\n      this.selectedHour = event.target.value;\n    }\n    static #_ = this.ɵfac = function MyStudyGroupComponent_Factory(t) {\n      return new (t || MyStudyGroupComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.StudyGroupService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MyStudyGroupComponent,\n      selectors: [[\"app-my-study-group\"]],\n      viewQuery: function MyStudyGroupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(MatCheckbox, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.time = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkboxes = _t);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 48,\n      vars: 5,\n      consts: [[\"input\", \"\"], [\"auto\", \"matAutocomplete\"], [\"menu\", \"matMenu\"], [\"menuHora\", \"matMenu\"], [\"time\", \"\"], [1, \"search_container\"], [1, \"search_row\"], [\"appearance\", \"outline\", 1, \"search_row_search_bar\"], [\"matInput\", \"\", 1, \"search_row_search_bar\", 3, \"input\", \"focus\", \"matAutocomplete\"], [\"requireSelection\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"search_row_search_item\", 3, \"click\"], [1, \"search_row_icon\"], [1, \"search_row_2\"], [\"mat-button\", \"\", 1, \"pesquisarButton\", 3, \"matMenuTriggerFor\"], [2, \"max-width\", \"auto !important\"], [1, \"position\"], [2, \"display\", \"flex\"], [\"color\", \"primary\", 3, \"click\"], [\"appearance\", \"outline\", 1, \"input6\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"time\", 3, \"change\"], [1, \"container\"], [4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [3, \"studyGroup\"]],\n      template: function MyStudyGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"mat-form-field\", 7)(3, \"mat-label\");\n          i0.ɵɵtext(4, \"Disciplina\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"input\", 8, 0);\n          i0.ɵɵlistener(\"input\", function MyStudyGroupComponent_Template_input_input_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filter());\n          })(\"focus\", function MyStudyGroupComponent_Template_input_focus_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filter());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-autocomplete\", 9, 1);\n          i0.ɵɵtemplate(9, MyStudyGroupComponent_mat_option_9_Template, 2, 3, \"mat-option\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.applyFilters());\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\", 12);\n          i0.ɵɵtext(12, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.clearFilters());\n          });\n          i0.ɵɵtext(14, \"Limpar Filtro \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"button\", 14);\n          i0.ɵɵtext(17, \"Dias da Semana\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-menu\", 15, 2)(20, \"div\", 16)(21, \"section\", 17)(22, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"dom\"));\n          });\n          i0.ɵɵtext(23, \"DOM\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"seg\"));\n          });\n          i0.ɵɵtext(25, \"SEG\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"ter\"));\n          });\n          i0.ɵɵtext(27, \"TER\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"qua\"));\n          });\n          i0.ɵɵtext(29, \"QUA\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"qui\"));\n          });\n          i0.ɵɵtext(31, \"QUI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_32_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"sex\"));\n          });\n          i0.ɵɵtext(33, \"SEX\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-checkbox\", 18);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_checkbox_click_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.stopPropagation();\n            return i0.ɵɵresetView(ctx.days(\"sab\"));\n          });\n          i0.ɵɵtext(35, \"SAB\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"button\", 14);\n          i0.ɵɵtext(37, \"Hora de In\\u00EDcio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"mat-menu\", 16, 3)(40, \"div\", 16)(41, \"mat-form-field\", 19);\n          i0.ɵɵlistener(\"click\", function MyStudyGroupComponent_Template_mat_form_field_click_41_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.stopPropagation());\n          });\n          i0.ɵɵelementStart(42, \"mat-label\");\n          i0.ɵɵtext(43, \"A partir de:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"input\", 20, 4);\n          i0.ɵɵlistener(\"change\", function MyStudyGroupComponent_Template_input_change_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onHourChange($event));\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(46, \"div\", 21);\n          i0.ɵɵtemplate(47, MyStudyGroupComponent_div_47_Template, 2, 1, \"div\", 22);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const auto_r4 = i0.ɵɵreference(8);\n          const menu_r5 = i0.ɵɵreference(19);\n          const menuHora_r6 = i0.ɵɵreference(39);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matAutocomplete\", auto_r4);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", menu_r5);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", menuHora_r6);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.options);\n        }\n      },\n      dependencies: [MatFormField, MatLabel, MatInput, MatButton, MatIcon, MatMenuModule, i3.MatMenu, i3.MatMenuTrigger, MatCheckboxModule, i4.MatCheckbox, NgFor, StudyGroupSearchItemComponent, MatAutocompleteModule, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger],\n      styles: [\".search_container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;padding:16px}.search_row[_ngcontent-%COMP%], .search_row_2[_ngcontent-%COMP%]{display:flex;height:56px;width:100%;gap:1%}.search_row_search_bar[_ngcontent-%COMP%]{height:100%;width:70%}.search_row_search_item[_ngcontent-%COMP%]{height:100%;width:15%}.search_row_icon[_ngcontent-%COMP%]{margin:0!important}.position[_ngcontent-%COMP%]{justify-content:center;display:flex;margin-top:15px;padding:0 5px}  .cdk-overlay-pane .mat-mdc-menu-panel{max-width:-moz-fit-content;max-width:fit-content}.pesquisarButton[_ngcontent-%COMP%]{margin:10px;color:#fff!important;background:#3f51b5;width:200px;height:50px}.fabButton[_ngcontent-%COMP%]{left:80%;border-radius:14px;position:fixed;top:80%;z-index:10;background:#e0e0ff;height:50px;border:none;padding-left:14px!important;box-shadow:0 3px 5px -1px #0003,0 6px 10px #00000024,0 1px 18px #0000001f}.input6[_ngcontent-%COMP%]{width:6rem}.container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;justify-content:center;align-items:stretch;width:100%;padding:16px;gap:0}@media (min-width: 768px){.search_row_2[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;justify-content:center;align-items:stretch;height:56px;width:100%;gap:1%}}\"]\n    });\n  }\n  return MyStudyGroupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}