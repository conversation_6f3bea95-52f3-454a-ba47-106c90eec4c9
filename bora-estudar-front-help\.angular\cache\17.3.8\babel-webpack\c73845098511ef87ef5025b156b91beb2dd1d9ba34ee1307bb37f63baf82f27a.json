{"ast": null, "code": "import { catchError, map, of } from 'rxjs';\nimport { inject } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nconst AUTH_API = '/api';\nconst httpOptions = {\n  headers: new HttpHeaders({\n    'Content-Type': 'application/json'\n  })\n};\nexport let StudyGroupService = /*#__PURE__*/(() => {\n  class StudyGroupService {\n    constructor() {\n      this.studyGroups = [];\n      this.myStudyGroups = [];\n      this.subjects = [];\n      this.http = inject(HttpClient);\n    }\n    getStudyGroups() {\n      return this.http.post(`${AUTH_API}/study-groups/filter`, {}, httpOptions).pipe(map(studyGroups => studyGroups.map(this.mappingStudyGroup)), catchError(error => {\n        console.error('Erro na chamada de API', error);\n        return of([]);\n      }));\n    }\n    getStudyGroupsFind(studentId) {\n      const requestBody = {\n        studentId\n      };\n      return this.http.post(`${AUTH_API}/study-groups/filter`, requestBody, httpOptions).pipe(map(studyGroups => studyGroups.map(this.mappingStudyGroup)), catchError(error => {\n        console.error('Erro na chamada de API', error);\n        return of([]);\n      }));\n      ;\n    }\n    mappingStudyGroup(item) {\n      const {\n        id,\n        description,\n        tutor,\n        subject,\n        students,\n        maxStudents,\n        meetingTime,\n        modality,\n        weekdays,\n        ownerId,\n        discordInviteUrl\n      } = item;\n      // const shortDescription =\n      //   description.length > 50 ? description.substr(0, 100) + '...' : description;\n      let countTutor = 0;\n      if (tutor) {\n        countTutor = 1;\n      }\n      const monitorText = `${countTutor}/${1}`;\n      const participantsCount = students.length;\n      const participantsText = `${participantsCount}/${maxStudents}`;\n      const mappedHour = meetingTime.substr(0, 5);\n      let mappedModality = '';\n      switch (modality) {\n        case 'REMOTE':\n          mappedModality = 'remoto';\n          break;\n        case 'PRESENCIAL':\n          mappedModality = 'presencial';\n          break;\n        default:\n          mappedModality = 'híbrido';\n      }\n      return {\n        id: id,\n        title: subject.name,\n        code: subject.code,\n        ownerId: ownerId,\n        shortDescription: description,\n        modality: mappedModality,\n        hour: mappedHour,\n        monitor: monitorText,\n        participants: participantsText,\n        students: students,\n        daysOfWeek: weekdays.map(day => day.name.toLowerCase().substring(0, 3)),\n        discordInviteUrl: discordInviteUrl\n      };\n    }\n    joinGroupService(groupId, id) {\n      return this.http.post(`${AUTH_API}/study-groups/${groupId}/students/${id}/join`, {}, httpOptions);\n    }\n    leaveGroupService(groupId, id) {\n      return this.http.post(`${AUTH_API}/study-groups/${groupId}/students/${id}/leave`, {}, httpOptions);\n    }\n    getSubjects() {\n      return this.http.get(`${AUTH_API}/subjects`);\n    }\n    getStudyGroupId(studyGroupId) {\n      return this.http.get(`${AUTH_API}/study-groups/${studyGroupId}`);\n    }\n    createStudyGroup(studyGroupData) {\n      return this.http.post(`${AUTH_API}/study-groups`, studyGroupData);\n    }\n    editStudyGroup(studyGroupData, groupId) {\n      return this.http.put(`${AUTH_API}/study-groups/${groupId}`, studyGroupData);\n    }\n    setStudyGroup(data) {\n      this.studyGroupParam = data;\n    }\n    getStudyGroup() {\n      return this.studyGroupParam;\n    }\n    static #_ = this.ɵfac = function StudyGroupService_Factory(t) {\n      return new (t || StudyGroupService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: StudyGroupService,\n      factory: StudyGroupService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return StudyGroupService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}